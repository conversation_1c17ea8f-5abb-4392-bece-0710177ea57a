#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试邮件内容选项修复
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_email_content_logic():
    """测试邮件内容选择逻辑"""
    try:
        # 导入主模块
        from alarm_monitor_pyside6 import EMAIL_CONTENT_OPTIONS
        
        print("=" * 60)
        print("邮件内容选择逻辑测试")
        print("=" * 60)
        
        # 模拟不同内容类型的逻辑
        test_cases = [
            ('all', ['text', 'image', 'excel']),
            ('text_only', ['text']),
            ('image_only', ['image']),
            ('excel_only', ['excel']),
            ('text_image', ['text', 'image']),
            ('text_excel', ['text', 'excel']),
            ('image_excel', ['image', 'excel'])
        ]
        
        print("内容类型 -> 应该包含的组件:")
        for content_type, expected_components in test_cases:
            # 模拟逻辑判断
            needs_image = content_type in ['all', 'image_only', 'text_image', 'image_excel']
            needs_excel = content_type in ['all', 'excel_only', 'text_excel', 'image_excel']
            needs_text = content_type in ['all', 'text_only', 'text_image', 'text_excel']
            
            actual_components = []
            if needs_text:
                actual_components.append('text')
            if needs_image:
                actual_components.append('image')
            if needs_excel:
                actual_components.append('excel')
            
            # 检查逻辑是否正确
            if set(actual_components) == set(expected_components):
                status = "✅"
            else:
                status = "❌"
            
            description = EMAIL_CONTENT_OPTIONS.get(content_type, '未知')
            print(f"{status} {content_type:12} -> {actual_components} | {description}")
        
        print("\n" + "=" * 60)
        print("✅ 邮件内容选择逻辑测试通过")
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_file():
    """测试配置文件"""
    try:
        import configparser
        
        print("\n" + "=" * 60)
        print("配置文件测试")
        print("=" * 60)
        
        config_file = "monitor_config.ini"
        if os.path.exists(config_file):
            cfg = configparser.ConfigParser()
            cfg.read(config_file, encoding='utf-8')
            
            if cfg.has_section('email'):
                content_type = cfg.get('email', 'content_type', fallback='未设置')
                print(f"当前配置的内容类型: {content_type}")
                
                from alarm_monitor_pyside6 import EMAIL_CONTENT_OPTIONS
                if content_type in EMAIL_CONTENT_OPTIONS:
                    print(f"配置有效: {EMAIL_CONTENT_OPTIONS[content_type]}")
                    return True
                else:
                    print(f"❌ 配置无效: {content_type}")
                    return False
            else:
                print("❌ 配置文件中没有email段")
                return False
        else:
            print("❌ 配置文件不存在")
            return False
            
    except Exception as e:
        print(f"配置文件测试失败: {e}")
        return False

def suggest_fix():
    """建议修复方案"""
    print("\n" + "=" * 60)
    print("🔧 修复建议")
    print("=" * 60)
    print("""
问题分析：
1. 邮件生成逻辑没有根据content_type正确分支
2. compose_email_with_groups总是生成图片，不管用户选择
3. 需要为每种内容类型创建专门的邮件生成方法

修复方案：
1. 修改邮件发送主逻辑，根据content_type选择不同的邮件生成方法
2. 确保"仅文本"真的只生成文本内容
3. 确保"仅图片"真的只生成图片内容
4. 确保"仅Excel"真的只生成Excel附件

关键代码位置：
- maybe_send_email() 方法中的邮件生成逻辑
- compose_email_with_groups() 方法需要重构
- 需要添加专门的简化邮件生成方法
    """)

if __name__ == "__main__":
    success1 = test_email_content_logic()
    success2 = test_config_file()
    
    if not (success1 and success2):
        suggest_fix()
    
    print(f"\n{'🎉 测试通过' if success1 and success2 else '❌ 需要修复'}")
    sys.exit(0 if (success1 and success2) else 1)
