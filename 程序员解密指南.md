# 告警邮件加密方式变更 - 程序员解密指南

## 📢 重要通知

告警监控系统的邮件加密方式已从 **Base64编码** 升级为 **Unicode乱码加密**，请更新你的解密代码。

## 🔄 变更对比

### 旧版本（Base64）
```text
加密内容：Z0FBQUFBQm9vcGFpOUo2OEt1VzA3d3NVUkpjUzMwRHc4ODVXTlRRTjNaRS1GUTc4...
特点：明显的Base64格式，容易被识别
```

### 新版本（Unicode乱码）
```text
加密内容：☙☴★☁☐☔★☁☐☦☽☯☜☃☙☏☒☴☕☧☌☔☹☧☑☴☙☣☝☥☚☛☜☝☞☟☠☡☢☣☤☥☦☧...
特点：看起来像随机Unicode符号，完全无法识别为加密内容
```

## 🔧 技术原理

新的加密流程：
1. **AES加密**：原始内容 → 加密字节
2. **Base64编码**：加密字节 → Base64字符串
3. **Unicode映射**：Base64字符 → Unicode符号

解密流程（反向）：
1. **Unicode反映射**：Unicode符号 → Base64字符串
2. **Base64解码**：Base64字符串 → 加密字节
3. **AES解密**：加密字节 → 原始内容

## 💻 解密代码实现

### Python版本（推荐）

```python
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64

def decrypt_unicode_alarm_email(chaos_content, password):
    """
    解密Unicode乱码格式的告警邮件
    
    Args:
        chaos_content (str): Unicode乱码内容（从邮件中复制的符号）
        password (str): 解密密码
    
    Returns:
        str: 解密后的告警内容
    """
    
    # Step 1: Unicode符号到Base64字符的映射表
    symbol_to_char = {
        '☀': 'A', '☁': 'B', '☂': 'C', '☃': 'D', '☄': 'E', '★': 'F', '☆': 'G', '☇': 'H',
        '☈': 'I', '☉': 'J', '☊': 'K', '☋': 'L', '☌': 'M', '☍': 'N', '☎': 'O', '☏': 'P',
        '☐': 'Q', '☑': 'R', '☒': 'S', '☓': 'T', '☔': 'U', '☕': 'V', '☖': 'W', '☗': 'X',
        '☘': 'Y', '☙': 'Z', '☚': 'a', '☛': 'b', '☜': 'c', '☝': 'd', '☞': 'e', '☟': 'f',
        '☠': 'g', '☡': 'h', '☢': 'i', '☣': 'j', '☤': 'k', '☥': 'l', '☦': 'm', '☧': 'n',
        '☨': 'o', '☩': 'p', '☪': 'q', '☫': 'r', '☬': 's', '☭': 't', '☮': 'u', '☯': 'v',
        '☰': 'w', '☱': 'x', '☲': 'y', '☳': 'z', '☴': '0', '☵': '1', '☶': '2', '☷': '3',
        '☸': '4', '☹': '5', '☺': '6', '☻': '7', '☼': '8', '☽': '9', '☾': '+', '☿': '/',
        '♀': '='
    }
    
    # Step 2: 将Unicode符号转换回Base64字符串
    b64_data = ""
    for symbol in chaos_content:
        b64_data += symbol_to_char.get(symbol, 'A')  # 未知符号默认为'A'
    
    # Step 3: 解码Base64得到加密字节
    try:
        encrypted_bytes = base64.b64decode(b64_data.encode('ascii'))
    except Exception as e:
        raise ValueError(f"Base64解码失败: {e}")
    
    # Step 4: 生成解密密钥（与加密时使用相同的参数）
    salt = b'alarm_monitor_salt_2024'  # 固定盐值
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,  # 迭代次数必须与加密时一致
    )
    key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
    
    # Step 5: AES解密
    try:
        f = Fernet(key)
        decrypted_bytes = f.decrypt(encrypted_bytes)
        return decrypted_bytes.decode('utf-8')
    except Exception as e:
        raise ValueError(f"解密失败，请检查密码是否正确: {e}")

# 使用示例
if __name__ == "__main__":
    # 从邮件中复制的Unicode乱码内容
    chaos_content = "☙☴★☁☐☔★☁☐☦☽☯☜☃☙☏☒☴☕☧☌☔☹☧☑☴☙☣☝☥..."
    
    # 解密密码
    password = "xjx001515"
    
    try:
        decrypted_content = decrypt_unicode_alarm_email(chaos_content, password)
        print("解密成功！")
        print("=" * 50)
        print(decrypted_content)
    except ValueError as e:
        print(f"解密失败: {e}")
```

### JavaScript版本

```javascript
// 需要安装: npm install crypto-js
const CryptoJS = require('crypto-js');

function decryptUnicodeAlarmEmail(chaosContent, password) {
    // Unicode符号到Base64字符的映射表
    const symbolToChar = {
        '☀': 'A', '☁': 'B', '☂': 'C', '☃': 'D', '☄': 'E', '★': 'F', '☆': 'G', '☇': 'H',
        '☈': 'I', '☉': 'J', '☊': 'K', '☋': 'L', '☌': 'M', '☍': 'N', '☎': 'O', '☏': 'P',
        '☐': 'Q', '☑': 'R', '☒': 'S', '☓': 'T', '☔': 'U', '☕': 'V', '☖': 'W', '☗': 'X',
        '☘': 'Y', '☙': 'Z', '☚': 'a', '☛': 'b', '☜': 'c', '☝': 'd', '☞': 'e', '☟': 'f',
        '☠': 'g', '☡': 'h', '☢': 'i', '☣': 'j', '☤': 'k', '☥': 'l', '☦': 'm', '☧': 'n',
        '☨': 'o', '☩': 'p', '☪': 'q', '☫': 'r', '☬': 's', '☭': 't', '☮': 'u', '☯': 'v',
        '☰': 'w', '☱': 'x', '☲': 'y', '☳': 'z', '☴': '0', '☵': '1', '☶': '2', '☷': '3',
        '☸': '4', '☹': '5', '☺': '6', '☻': '7', '☼': '8', '☽': '9', '☾': '+', '☿': '/',
        '♀': '='
    };
    
    // 将Unicode符号转换回Base64
    let b64Data = '';
    for (let symbol of chaosContent) {
        b64Data += symbolToChar[symbol] || 'A';
    }
    
    try {
        // 解码Base64
        const encryptedData = atob(b64Data);
        
        // 生成密钥（简化版本，实际项目中需要使用PBKDF2）
        const key = CryptoJS.PBKDF2(password, 'alarm_monitor_salt_2024', {
            keySize: 256/32,
            iterations: 100000
        });
        
        // AES解密
        const decrypted = CryptoJS.AES.decrypt(encryptedData, key, {
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        });
        
        return decrypted.toString(CryptoJS.enc.Utf8);
    } catch (error) {
        throw new Error(`解密失败: ${error.message}`);
    }
}

// 使用示例
const chaosContent = "☙☴★☁☐☔★☁☐☦☽☯☜☃☙☏☒☴☕☧☌☔☹☧☑☴☙☣☝☥...";
const password = "xjx001515";

try {
    const decryptedContent = decryptUnicodeAlarmEmail(chaosContent, password);
    console.log("解密成功！");
    console.log("=".repeat(50));
    console.log(decryptedContent);
} catch (error) {
    console.error(`解密失败: ${error.message}`);
}
```

### Java版本

```java
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

public class UnicodeAlarmDecryptor {
    
    private static final Map<Character, Character> SYMBOL_TO_CHAR = new HashMap<>();
    
    static {
        // 初始化映射表
        SYMBOL_TO_CHAR.put('☀', 'A'); SYMBOL_TO_CHAR.put('☁', 'B'); SYMBOL_TO_CHAR.put('☂', 'C');
        SYMBOL_TO_CHAR.put('☃', 'D'); SYMBOL_TO_CHAR.put('☄', 'E'); SYMBOL_TO_CHAR.put('★', 'F');
        SYMBOL_TO_CHAR.put('☆', 'G'); SYMBOL_TO_CHAR.put('☇', 'H'); SYMBOL_TO_CHAR.put('☈', 'I');
        SYMBOL_TO_CHAR.put('☉', 'J'); SYMBOL_TO_CHAR.put('☊', 'K'); SYMBOL_TO_CHAR.put('☋', 'L');
        SYMBOL_TO_CHAR.put('☌', 'M'); SYMBOL_TO_CHAR.put('☍', 'N'); SYMBOL_TO_CHAR.put('☎', 'O');
        SYMBOL_TO_CHAR.put('☏', 'P'); SYMBOL_TO_CHAR.put('☐', 'Q'); SYMBOL_TO_CHAR.put('☑', 'R');
        SYMBOL_TO_CHAR.put('☒', 'S'); SYMBOL_TO_CHAR.put('☓', 'T'); SYMBOL_TO_CHAR.put('☔', 'U');
        SYMBOL_TO_CHAR.put('☕', 'V'); SYMBOL_TO_CHAR.put('☖', 'W'); SYMBOL_TO_CHAR.put('☗', 'X');
        SYMBOL_TO_CHAR.put('☘', 'Y'); SYMBOL_TO_CHAR.put('☙', 'Z'); SYMBOL_TO_CHAR.put('☚', 'a');
        SYMBOL_TO_CHAR.put('☛', 'b'); SYMBOL_TO_CHAR.put('☜', 'c'); SYMBOL_TO_CHAR.put('☝', 'd');
        SYMBOL_TO_CHAR.put('☞', 'e'); SYMBOL_TO_CHAR.put('☟', 'f'); SYMBOL_TO_CHAR.put('☠', 'g');
        SYMBOL_TO_CHAR.put('☡', 'h'); SYMBOL_TO_CHAR.put('☢', 'i'); SYMBOL_TO_CHAR.put('☣', 'j');
        SYMBOL_TO_CHAR.put('☤', 'k'); SYMBOL_TO_CHAR.put('☥', 'l'); SYMBOL_TO_CHAR.put('☦', 'm');
        SYMBOL_TO_CHAR.put('☧', 'n'); SYMBOL_TO_CHAR.put('☨', 'o'); SYMBOL_TO_CHAR.put('☩', 'p');
        SYMBOL_TO_CHAR.put('☪', 'q'); SYMBOL_TO_CHAR.put('☫', 'r'); SYMBOL_TO_CHAR.put('☬', 's');
        SYMBOL_TO_CHAR.put('☭', 't'); SYMBOL_TO_CHAR.put('☮', 'u'); SYMBOL_TO_CHAR.put('☯', 'v');
        SYMBOL_TO_CHAR.put('☰', 'w'); SYMBOL_TO_CHAR.put('☱', 'x'); SYMBOL_TO_CHAR.put('☲', 'y');
        SYMBOL_TO_CHAR.put('☳', 'z'); SYMBOL_TO_CHAR.put('☴', '0'); SYMBOL_TO_CHAR.put('☵', '1');
        SYMBOL_TO_CHAR.put('☶', '2'); SYMBOL_TO_CHAR.put('☷', '3'); SYMBOL_TO_CHAR.put('☸', '4');
        SYMBOL_TO_CHAR.put('☹', '5'); SYMBOL_TO_CHAR.put('☺', '6'); SYMBOL_TO_CHAR.put('☻', '7');
        SYMBOL_TO_CHAR.put('☼', '8'); SYMBOL_TO_CHAR.put('☽', '9'); SYMBOL_TO_CHAR.put('☾', '+');
        SYMBOL_TO_CHAR.put('☿', '/'); SYMBOL_TO_CHAR.put('♀', '=');
    }
    
    public static String decryptUnicodeAlarmEmail(String chaosContent, String password) 
            throws Exception {
        
        // 将Unicode符号转换回Base64
        StringBuilder b64Data = new StringBuilder();
        for (char symbol : chaosContent.toCharArray()) {
            b64Data.append(SYMBOL_TO_CHAR.getOrDefault(symbol, 'A'));
        }
        
        // 解码Base64
        byte[] encryptedBytes = Base64.getDecoder().decode(b64Data.toString());
        
        // 生成密钥（简化版本，实际需要使用PBKDF2）
        byte[] keyBytes = password.getBytes();
        SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "AES");
        
        // AES解密
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.DECRYPT_MODE, keySpec);
        byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
        
        return new String(decryptedBytes, "UTF-8");
    }
    
    public static void main(String[] args) {
        String chaosContent = "☙☴★☁☐☔★☁☐☦☽☯☜☃☙☏☒☴☕☧☌☔☹☧☑☴☙☣☝☥...";
        String password = "xjx001515";
        
        try {
            String decryptedContent = decryptUnicodeAlarmEmail(chaosContent, password);
            System.out.println("解密成功！");
            System.out.println("=".repeat(50));
            System.out.println(decryptedContent);
        } catch (Exception e) {
            System.err.println("解密失败: " + e.getMessage());
        }
    }
}
```

## 📋 使用步骤

### 1. 从邮件中提取加密内容
```text
从邮件中复制 -----BEGIN ENCRYPTED CONTENT----- 和 -----END ENCRYPTED CONTENT----- 之间的Unicode符号
```

### 2. 调用解密函数
```python
chaos_content = "☙☴★☁☐☔★☁☐☦☽☯☜☃☙☏☒☴☕☧☌☔☹☧☑☴☙☣☝☥..."
password = "xjx001515"
decrypted = decrypt_unicode_alarm_email(chaos_content, password)
```

### 3. 处理解密结果
```python
print(decrypted)  # 输出原始告警内容
```

## ⚠️ 重要注意事项

### 1. **依赖库要求**
- **Python**: `pip install cryptography`
- **JavaScript**: `npm install crypto-js`
- **Java**: 使用内置的加密库

### 2. **关键参数**
- **盐值**: `alarm_monitor_salt_2024` （固定值，不能修改）
- **迭代次数**: `100000` （必须与加密时一致）
- **算法**: `AES + PBKDF2 + SHA256`

### 3. **错误处理**
- 密码错误会抛出解密异常
- Unicode符号损坏会导致Base64解码失败
- 建议添加完整的异常处理

### 4. **性能考虑**
- PBKDF2迭代10万次，解密需要一定时间
- 大量解密时考虑缓存密钥
- 建议在后台线程中执行解密

## 🔍 调试技巧

### 1. **验证Unicode映射**
```python
# 检查符号是否在映射表中
for symbol in chaos_content:
    if symbol not in symbol_to_char:
        print(f"未知符号: {symbol} (Unicode: {ord(symbol)})")
```

### 2. **验证Base64格式**
```python
# 检查转换后的Base64是否有效
try:
    base64.b64decode(b64_data)
    print("Base64格式正确")
except Exception as e:
    print(f"Base64格式错误: {e}")
```

### 3. **分步调试**
```python
print(f"1. Unicode长度: {len(chaos_content)}")
print(f"2. Base64长度: {len(b64_data)}")
print(f"3. 加密字节长度: {len(encrypted_bytes)}")
print(f"4. 解密结果长度: {len(decrypted_content)}")
```

## 📞 技术支持

如果在实现过程中遇到问题，请提供：
1. 使用的编程语言和版本
2. 具体的错误信息
3. 测试用的Unicode符号片段
4. 解密代码的实现

---

**更新时间**: 2024-08-18  
**版本**: v2.0 Unicode乱码加密  
**联系人**: 系统管理员
