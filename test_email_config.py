#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试邮件配置功能
验证发送模式配置是否正常工作
"""

import sys
import os
import configparser

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_config_read_write():
    """测试配置文件的读写功能"""
    print("🧪 测试邮件配置读写功能...")
    
    config_file = "test_monitor_config.ini"
    
    # 创建测试配置
    config = configparser.ConfigParser()
    
    # 添加邮件配置
    config['email'] = {
        'enabled': 'true',
        'smtp_host': 'smtp.test.com',
        'smtp_port': '465',
        'use_ssl': 'true',
        'username': '<EMAIL>',
        'password': 'test_password',
        'from_addr': '<EMAIL>',
        'to_addr': '<EMAIL>',
        'send_mode': 'split'  # 测试拆分模式
    }
    
    # 写入配置文件
    with open(config_file, 'w', encoding='utf-8') as f:
        config.write(f)
    
    print(f"✅ 配置文件已创建: {config_file}")
    
    # 读取配置文件
    read_config = configparser.ConfigParser()
    read_config.read(config_file, encoding='utf-8')
    
    # 验证配置
    send_mode = read_config.get('email', 'send_mode', fallback='merged')
    print(f"📧 读取到的发送模式: {send_mode}")
    
    if send_mode == 'split':
        print("✅ 拆分发送模式配置正确")
    else:
        print("❌ 发送模式配置错误")
    
    # 测试修改配置
    read_config.set('email', 'send_mode', 'merged')
    with open(config_file, 'w', encoding='utf-8') as f:
        read_config.write(f)
    
    # 再次读取验证
    verify_config = configparser.ConfigParser()
    verify_config.read(config_file, encoding='utf-8')
    new_send_mode = verify_config.get('email', 'send_mode', fallback='split')
    
    print(f"📧 修改后的发送模式: {new_send_mode}")
    
    if new_send_mode == 'merged':
        print("✅ 合并发送模式配置正确")
    else:
        print("❌ 发送模式修改失败")
    
    # 清理测试文件
    try:
        os.remove(config_file)
        print(f"🗑️ 测试文件已清理: {config_file}")
    except:
        pass
    
    return True

def test_email_mode_logic():
    """测试邮件发送模式逻辑"""
    print("\n🧪 测试邮件发送模式逻辑...")
    
    # 模拟不同的发送模式
    test_cases = [
        ('split', '拆分发送'),
        ('merged', '合并发送'),
        ('invalid', '拆分发送'),  # 无效值应该回退到默认值
        (None, '拆分发送')  # 空值应该回退到默认值
    ]
    
    for mode_value, expected_desc in test_cases:
        # 模拟获取配置的逻辑
        if mode_value == 'split':
            actual_mode = 'split'
        elif mode_value == 'merged':
            actual_mode = 'merged'
        else:
            actual_mode = 'split'  # 默认值
        
        if actual_mode == 'split':
            actual_desc = '拆分发送'
        else:
            actual_desc = '合并发送'
        
        print(f"  配置值: {mode_value} -> 实际模式: {actual_desc}")
        
        if actual_desc == expected_desc:
            print(f"  ✅ 模式 '{mode_value}' 处理正确")
        else:
            print(f"  ❌ 模式 '{mode_value}' 处理错误，期望: {expected_desc}, 实际: {actual_desc}")
    
    return True

def simulate_email_sending():
    """模拟不同发送模式的邮件发送"""
    print("\n🚀 模拟不同发送模式的邮件发送...")
    
    # 模拟告警数据
    mock_groups = {
        'group_001': [{'alarm_key': 'alarm_001', 'code_name': '小区退服'}],
        'group_002': [{'alarm_key': 'alarm_002', 'code_name': '天馈异常'}, 
                      {'alarm_key': 'alarm_003', 'code_name': '光功率异常'}],
        'group_003': [{'alarm_key': 'alarm_004', 'code_name': '链路故障'}]
    }
    
    total_alarms = sum(len(alarms) for alarms in mock_groups.values())
    
    print(f"📊 模拟数据: {total_alarms} 条告警，分为 {len(mock_groups)} 组")
    
    # 测试拆分发送模式
    print("\n📧 模拟拆分发送模式:")
    print(f"  将发送 {len(mock_groups)} 封独立邮件")
    for i, (group_key, group_alarms) in enumerate(mock_groups.items(), 1):
        print(f"  邮件 {i}: 组 {group_key} - {len(group_alarms)} 条告警")
    
    # 测试合并发送模式
    print("\n📧 模拟合并发送模式:")
    print(f"  将发送 1 封合并邮件，包含所有 {total_alarms} 条告警")
    print(f"  邮件内容将包含 {len(mock_groups)} 个告警组")
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 邮件配置功能测试")
    print("=" * 60)
    
    try:
        # 测试配置读写
        test_config_read_write()
        
        # 测试发送模式逻辑
        test_email_mode_logic()
        
        # 模拟邮件发送
        simulate_email_sending()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试通过！邮件配置功能正常")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
