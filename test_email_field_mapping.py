#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试邮件字段映射问题
"""

import sys
import os
from datetime import datetime

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_email_field_mapping():
    """测试邮件字段映射是否正确"""
    print("🧪 测试邮件字段映射")
    print("=" * 50)
    
    # 模拟从数据库获取的告警数据（与程序界面显示一致）
    mock_alarms = [
        {
            'code_name': 'LTE小区退出服务',
            'position_name': 'HZYC0245-ZX-F9HS9-(郓城唐庙支局-郓城唐庙)-B1',
            'me_name': 'HZYC0023-ZX-F9H11-(郓城唐庙支局-郓城唐庙-郓城唐庙毕庄-郓城唐庙后刘-郓城唐庙江楼)(3190069)',
            'time_str': '08-18 10:30',  # 程序界面显示的格式
            'duration_str': '2小时30分钟',  # 程序界面显示的格式
            'relation_marks': '🟡衍生←输入电源断 🎯重点',
            'alarm_raised_time': 1692345000,  # 原始时间戳
            'effective_duration_minutes': 150,  # 原始分钟数
            'raw_data': {
                'positionname': '备用位置信息'
            }
        },
        {
            'code_name': '',  # 空的告警名称
            'position_name': 'HZYC0245-ZX-F9HS9-(郓城唐庙支局-郓城唐庙)-A1',
            'me_name': '',  # 空的网元名称
            'time_str': '08-18 10:35',
            'duration_str': '',  # 空的持续时间
            'relation_marks': '',  # 空的关联标记
            'raw_data': {}
        },
        {
            'code_name': '输入电源断',
            'position_name': '',  # 空的位置信息
            'me_name': '基站设备003',
            'time_str': '08-18 10:25',
            'duration_str': '2小时35分钟',
            'relation_marks': '🔴根源',
            'raw_data': {
                'positionname': '从raw_data获取的位置'
            }
        }
    ]
    
    # 修复后的邮件内容生成逻辑
    def generate_fixed_email_content(group_alarms):
        import uuid
        
        content_lines = []
        content_lines.append(f"告警监控系统 - 独立告警通知")
        content_lines.append(f"发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        content_lines.append(f"根源分组ID: 41d89e")
        content_lines.append(f"告警数量: {len(group_alarms)} 条")
        content_lines.append("")
        content_lines.append("=" * 60)
        content_lines.append("")
        
        # 修复后的告警详情生成
        for i, alarm in enumerate(group_alarms, 1):
            content_lines.append(f"告警 {i}:")
            
            # 告警名称 - 确保不为空
            code_name = alarm.get('code_name', '') or '未知告警'
            if code_name and code_name != '未知告警':
                content_lines.append(f"  告警名称: {code_name}")

            # 位置信息（优先级：position_name > raw_data.positionname > 未知位置）
            position_info = (alarm.get('position_name')
                           or alarm.get('raw_data', {}).get('positionname')
                           or '未知位置')
            if position_info and position_info != '未知位置':
                content_lines.append(f"  位置信息: {position_info}")

            # 网元名称 - 确保不为空
            me_name = alarm.get('me_name', '') or '未知网元'
            if me_name and me_name != '未知网元':
                content_lines.append(f"  网元名称: {me_name}")

            # 发生时间 - 使用多种时间字段
            alarm_time = None
            # 优先使用已格式化的时间字符串
            if alarm.get('time_str') and alarm.get('time_str') != '未知':
                alarm_time = alarm.get('time_str')
                # 如果是短格式(MM-DD HH:MM)，尝试补全年份
                if len(alarm_time) == 11 and '-' in alarm_time and ':' in alarm_time:
                    current_year = datetime.now().year
                    alarm_time = f"{current_year}-{alarm_time}"
            else:
                # 尝试从原始时间戳转换
                raw_time = alarm.get('alarm_raised_time')
                if raw_time and raw_time != '未知':
                    try:
                        if isinstance(raw_time, (int, float)):
                            timestamp = raw_time
                            if timestamp > 1e12:  # 毫秒时间戳
                                timestamp = timestamp / 1000
                            alarm_time = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                        elif isinstance(raw_time, str) and raw_time.isdigit():
                            timestamp = float(raw_time)
                            if timestamp > 1e12:  # 毫秒时间戳
                                timestamp = timestamp / 1000
                            alarm_time = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        pass
            
            if alarm_time:
                content_lines.append(f"  发生时间: {alarm_time}")

            # 持续时间信息
            duration_text = None
            # 优先使用已格式化的持续时间
            if alarm.get('duration_str') and alarm.get('duration_str') != '未知':
                duration_text = alarm.get('duration_str')
            else:
                # 使用数值计算
                duration_minutes = alarm.get('effective_duration_minutes', 0)
                if duration_minutes and duration_minutes > 0:
                    # 简化的持续时间格式化
                    hours = duration_minutes // 60
                    mins = duration_minutes % 60
                    if hours > 0:
                        duration_text = f"{hours}小时{mins}分钟" if mins > 0 else f"{hours}小时"
                    else:
                        duration_text = f"{mins}分钟"
            
            if duration_text:
                content_lines.append(f"  持续时间: {duration_text}")

            # 关联标记
            relation_marks = alarm.get('relation_marks', '')
            if relation_marks:
                content_lines.append(f"  关联标记: {relation_marks}")

            content_lines.append("")
        
        content_lines.append("=" * 60)
        content_lines.append("")
        content_lines.append("请及时处理相关告警。")
        content_lines.append("")
        content_lines.append(f"<!-- UUID: {str(uuid.uuid4())} -->")
        
        return "\n".join(content_lines)
    
    # 生成修复后的邮件内容
    email_content = generate_fixed_email_content(mock_alarms)
    
    print("📧 修复后的邮件内容:")
    print("-" * 40)
    print(email_content)
    print("-" * 40)
    
    # 验证修复效果
    checks = [
        ("LTE小区退出服务" in email_content, "告警名称正确显示"),
        ("HZYC0245-ZX-F9HS9" in email_content, "位置信息正确显示"),
        ("HZYC0023-ZX-F9H11" in email_content, "网元名称正确显示"),
        ("2025-08-18" in email_content, "时间格式正确转换"),
        ("2小时30分钟" in email_content, "持续时间正确显示"),
        ("🟡衍生←输入电源断 🎯重点" in email_content, "关联标记正确显示"),
        ("输入电源断" in email_content, "根源告警正确显示"),
        ("🔴根源" in email_content, "根源标记正确显示"),
        ("从raw_data获取的位置" in email_content, "位置信息回退逻辑正确"),
    ]
    
    print("\n✅ 修复验证:")
    all_passed = True
    for check, desc in checks:
        status = "✅" if check else "❌"
        print(f"  {status} {desc}")
        if not check:
            all_passed = False
    
    return all_passed

def compare_old_vs_new():
    """对比修复前后的差异"""
    print("\n🔄 修复前后对比")
    print("=" * 50)
    
    print("❌ 修复前的问题:")
    print("  • 告警名称为空时不显示")
    print("  • 网元名称为空时不显示")
    print("  • 时间字段映射错误，显示'未知'")
    print("  • 持续时间字段映射错误")
    print("  • 位置信息回退逻辑不完整")
    print("  • 关联标记可能丢失")
    
    print("\n✅ 修复后的改进:")
    print("  • 优先使用已格式化的字段(time_str, duration_str)")
    print("  • 空字段时智能跳过，避免显示'未知'")
    print("  • 时间格式自动补全年份")
    print("  • 位置信息多级回退逻辑")
    print("  • 确保所有有价值的字段都能正确显示")
    
    print("\n🎯 关键修复点:")
    print("  1. 字段映射: alarm_raised_time → time_str (优先)")
    print("  2. 时间处理: MM-DD HH:MM → YYYY-MM-DD HH:MM")
    print("  3. 空值处理: 空字段不显示，而不是显示'未知'")
    print("  4. 数据优先级: 已格式化字段 > 原始字段 > 默认值")

if __name__ == "__main__":
    print("🔍 邮件字段映射问题测试")
    print("=" * 60)
    
    # 测试字段映射修复
    test1 = test_email_field_mapping()
    
    # 对比修复前后
    compare_old_vs_new()
    
    print("\n" + "=" * 60)
    if test1:
        print("🎉 邮件字段映射修复成功！")
        print("\n📧 现在邮件将正确显示:")
        print("• ✅ 完整的告警名称")
        print("• ✅ 详细的位置信息")
        print("• ✅ 准确的网元名称")
        print("• ✅ 正确的发生时间")
        print("• ✅ 精确的持续时间")
        print("• ✅ 完整的关联标记")
        print("\n🚀 用户将能看到与程序界面一致的完整告警信息！")
    else:
        print("⚠️ 字段映射仍有问题，需要进一步检查")
    print("=" * 60)
