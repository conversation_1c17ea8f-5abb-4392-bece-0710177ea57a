#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的邮件格式
验证字段调整是否正确
"""

import sys
import os
from datetime import datetime

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_new_email_format():
    """测试新的邮件格式"""
    print("🧪 测试新的邮件格式")
    print("=" * 50)
    
    # 模拟告警数据
    mock_alarms = [
        {
            'code_name': '小区退服告警',
            'position_name': '杭州市西湖区文三路',
            'me_name': '基站001',
            'ne_ip': '***********',  # 应该被去掉
            'perceived_severity_name': '严重',  # 应该被去掉
            'alarm_raised_time': 1692345000,  # 时间戳
            'effective_duration_minutes': 120,
            'relation_marks': '🔴根源',
            'operator_info': '联通',  # 应该被去掉
            'raw_data': {
                'positionname': '备用位置信息'
            }
        },
        {
            'code_name': '天馈驻波比异常',
            'position_name': '',  # 空值，应该使用raw_data中的
            'me_name': '基站002',
            'alarm_raised_time': '2024-08-18 11:00:00',  # 字符串格式
            'effective_duration_minutes': 60,
            'relation_marks': '🟡衍生←小区退服',
            'raw_data': {
                'positionname': '杭州市滨江区网商路'
            }
        },
        {
            'code_name': '光功率异常',
            'me_name': '基站003',
            'alarm_raised_time': '未知',
            'effective_duration_minutes': 0,  # 无持续时间
            'relation_marks': '',  # 无关联标记
            'raw_data': {}  # 无位置信息
        }
    ]
    
    # 生成邮件内容
    content = generate_test_email_content(mock_alarms)
    
    print("📧 生成的邮件内容:")
    print("-" * 40)
    print(content)
    print("-" * 40)
    
    # 验证格式
    checks = [
        ("告警名称:" in content, "包含告警名称"),
        ("位置信息:" in content, "包含位置信息"),
        ("网元名称:" in content, "包含网元名称"),
        ("发生时间:" in content, "包含发生时间"),
        ("关联标记:" in content, "包含关联标记"),
        ("IP地址:" not in content, "已去掉IP地址"),
        ("告警级别:" not in content, "已去掉告警级别"),
        ("运营商:" not in content, "已去掉运营商"),
        ("杭州市西湖区文三路" in content, "显示位置信息"),
        ("🔴根源" in content, "显示关联标记"),
        ("2024-08-18" in content, "时间格式正确"),
    ]
    
    print("\n✅ 格式验证:")
    all_passed = True
    for check, desc in checks:
        status = "✅" if check else "❌"
        print(f"  {status} {desc}")
        if not check:
            all_passed = False
    
    return all_passed

def generate_test_email_content(group_alarms):
    """生成测试邮件内容（模拟新格式）"""
    import uuid
    
    content_lines = []
    content_lines.append(f"告警监控系统 - 独立告警通知")
    content_lines.append(f"发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    content_lines.append(f"根源分组ID: test_group_123")
    content_lines.append(f"告警数量: {len(group_alarms)} 条")
    content_lines.append("")
    content_lines.append("=" * 60)
    content_lines.append("")
    
    # 添加告警详情（使用新格式）
    for i, alarm in enumerate(group_alarms, 1):
        content_lines.append(f"告警 {i}:")
        content_lines.append(f"  告警名称: {alarm.get('code_name', '未知')}")
        
        # 添加位置信息（优先级：position_name > raw_data.positionname > 未知位置）
        position_info = (alarm.get('position_name') 
                       or alarm.get('raw_data', {}).get('positionname') 
                       or '未知位置')
        content_lines.append(f"  位置信息: {position_info}")
        
        content_lines.append(f"  网元名称: {alarm.get('me_name', '未知')}")
        
        # 格式化发生时间
        alarm_time = alarm.get('alarm_raised_time', '未知')
        if alarm_time and alarm_time != '未知':
            try:
                # 如果是时间戳，转换为可读格式
                if isinstance(alarm_time, (int, float)):
                    timestamp = alarm_time
                    if timestamp > 1e12:  # 毫秒时间戳
                        timestamp = timestamp / 1000
                    alarm_time = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                elif isinstance(alarm_time, str) and alarm_time.isdigit():
                    timestamp = float(alarm_time)
                    if timestamp > 1e12:  # 毫秒时间戳
                        timestamp = timestamp / 1000
                    alarm_time = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
            except:
                pass  # 保持原值
        
        content_lines.append(f"  发生时间: {alarm_time}")

        # 添加持续时间信息
        duration_minutes = alarm.get('effective_duration_minutes', 0)
        if duration_minutes > 0:
            # 简化的持续时间格式化
            hours = duration_minutes // 60
            mins = duration_minutes % 60
            if hours > 0:
                duration_text = f"{hours}小时{mins}分钟" if mins > 0 else f"{hours}小时"
            else:
                duration_text = f"{mins}分钟"
            content_lines.append(f"  持续时间: {duration_text}")
        
        # 添加关联标记
        relation_marks = alarm.get('relation_marks', '')
        if relation_marks:
            content_lines.append(f"  关联标记: {relation_marks}")

        content_lines.append("")
    
    content_lines.append("=" * 60)
    content_lines.append("")
    content_lines.append("请及时处理相关告警。")
    content_lines.append("")
    content_lines.append(f"<!-- UUID: {str(uuid.uuid4())} -->")
    
    return "\n".join(content_lines)

def test_field_priority():
    """测试字段优先级逻辑"""
    print("\n🧪 测试字段优先级")
    print("=" * 50)
    
    test_cases = [
        {
            'name': '位置信息优先级测试',
            'alarm': {
                'position_name': '主要位置',
                'raw_data': {'positionname': '备用位置'}
            },
            'expected': '主要位置'
        },
        {
            'name': '位置信息回退测试',
            'alarm': {
                'position_name': '',
                'raw_data': {'positionname': '备用位置'}
            },
            'expected': '备用位置'
        },
        {
            'name': '位置信息缺失测试',
            'alarm': {
                'position_name': '',
                'raw_data': {}
            },
            'expected': '未知位置'
        }
    ]
    
    for case in test_cases:
        alarm = case['alarm']
        expected = case['expected']
        
        # 模拟优先级逻辑
        position_info = (alarm.get('position_name') 
                       or alarm.get('raw_data', {}).get('positionname') 
                       or '未知位置')
        
        result = position_info == expected
        status = "✅" if result else "❌"
        print(f"  {status} {case['name']}: 期望'{expected}', 实际'{position_info}'")
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 新邮件格式测试")
    print("=" * 60)
    
    try:
        # 测试新邮件格式
        test1 = test_new_email_format()
        
        # 测试字段优先级
        test2 = test_field_priority()
        
        print("\n" + "=" * 60)
        if all([test1, test2]):
            print("🎉 所有测试通过！新邮件格式正确")
            print("\n📧 新格式特点:")
            print("• ✅ 保留: 告警名称、网元名称、发生时间、持续时间、关联标记")
            print("• ➕ 新增: 位置信息（支持多级回退）")
            print("• ❌ 去掉: IP地址、告警级别、运营商")
            print("• 🔧 优化: 时间格式自动转换")
        else:
            print("⚠️ 部分测试失败，请检查实现")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
