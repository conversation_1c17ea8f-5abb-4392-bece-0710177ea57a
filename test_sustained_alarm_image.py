#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持续告警图片生成功能测试脚本
独立测试持续告警邮件中的图片生成模块
"""

import sys
import os
from datetime import datetime

def test_image_generation():
    """测试图片生成功能"""
    print("🔧 开始测试持续告警图片生成功能...")
    
    # 模拟EmailImageGenerator类
    class EmailImageGenerator:
        """模拟的图片生成器"""
        
        @staticmethod
        def create_text_image(text_content):
            """模拟图片生成"""
            try:
                print(f"📝 正在生成图片，文本长度: {len(text_content)} 字符")
                print(f"📝 文本行数: {len(text_content.split(chr(10)))} 行")
                
                # 模拟PIL库检查
                try:
                    from PIL import Image, ImageDraw, ImageFont
                    import io
                    print("✅ PIL库检查通过")
                    
                    # 创建一个简单的测试图片
                    img = Image.new('RGB', (400, 200), 'white')
                    draw = ImageDraw.Draw(img)
                    
                    try:
                        font = ImageFont.truetype("arial.ttf", 12)
                    except:
                        font = ImageFont.load_default()
                    
                    # 绘制测试文本
                    lines = text_content.split('\n')[:10]  # 只取前10行
                    y = 10
                    for line in lines:
                        draw.text((10, y), line[:50], fill='black', font=font)  # 只取前50个字符
                        y += 15
                    
                    # 保存到内存
                    img_buffer = io.BytesIO()
                    img.save(img_buffer, format='JPEG', quality=85, optimize=True)
                    img_buffer.seek(0)
                    
                    print(f"📸 图片生成成功，大小: {len(img_buffer.getvalue())} 字节")
                    return img_buffer
                    
                except ImportError:
                    print("❌ PIL库未安装，返回None")
                    return None
                    
            except Exception as e:
                print(f"❌ 图片生成异常: {e}")
                return None

    # 测试用的告警文本内容
    test_content = """持续告警通知 - TEST_DEVICE
发送时间: 2025-08-19 15:30:00
分组ID: test_group
持续告警数量: 1 条
最高阈值: 6小时

============================================================

告警 1:
  告警名称: 测试告警
  网元名称: TEST_DEVICE_001
  位置信息: 设备1 > 机架1 > 子架1 > 槽位6
  发生时间: 2025-08-19 08:53
  持续时间: 6小时53分钟
  关联标记: 🔴根源
  产品资源类型: 测试设备
  NBI ID: test-nbi-12345

============================================================

请及时处理相关告警。"""

    print("\n" + "="*60)
    print("🧪 测试场景1: 图片生成成功的情况")
    print("="*60)
    
    # 测试图片生成
    image_generator = EmailImageGenerator()
    
    # 模拟持续告警邮件中的图片生成逻辑
    try:
        print("🖼️ 持续告警图片附件功能已启用，开始生成图片...")
        
        # 这是修复后的代码逻辑
        from email.mime.image import MIMEImage
        from datetime import datetime
        print("✅ 导入模块成功")
        
        # 图片始终使用明文内容
        enhanced_text = test_content
        img_buffer = image_generator.create_text_image(enhanced_text)
        
        if img_buffer:
            img_data = img_buffer.getvalue()
            filename = f"sustained_alarm_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg"
            
            # 计算文本行数
            text_lines = len(enhanced_text.split('\n'))
            print(f"📸 持续告警图片附件生成成功: {filename}")
            print(f"📊 图片信息: {len(img_data)} 字节, {text_lines} 行文本, 明文显示")
            
            # 保存测试图片到文件
            with open(filename, 'wb') as f:
                f.write(img_data)
            print(f"💾 测试图片已保存到: {filename}")
            
        else:
            print("⚠️ 持续告警图片生成失败（可能缺少PIL库）")
            print("💡 提示: 请安装Pillow库: pip install Pillow")
            
    except Exception as e:
        print(f"❌ 持续告警图片附件生成失败: {e}")
        print("💡 建议: 检查PIL库是否正确安装")

def test_error_scenarios():
    """测试错误场景"""
    print("\n" + "="*60)
    print("🧪 测试场景2: 图片生成失败的情况")
    print("="*60)
    
    # 模拟图片生成失败的情况
    class FailingImageGenerator:
        @staticmethod
        def create_text_image(text_content):
            print("❌ 模拟图片生成失败")
            return None
    
    try:
        print("🖼️ 持续告警图片附件功能已启用，开始生成图片...")
        
        # 测试修复后的代码是否能正确处理失败情况
        from email.mime.image import MIMEImage
        from datetime import datetime
        print("✅ 导入模块成功")
        
        enhanced_text = "测试内容"
        failing_generator = FailingImageGenerator()
        img_buffer = failing_generator.create_text_image(enhanced_text)
        
        if img_buffer:
            # 这部分不应该执行
            print("❌ 意外：图片生成应该失败")
        else:
            print("✅ 正确处理图片生成失败的情况")
            print("⚠️ 持续告警图片生成失败（模拟失败）")
            
        # 关键测试：即使图片生成失败，datetime也应该可用
        test_filename = f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg"
        print(f"✅ datetime可用性测试通过: {test_filename}")
        
    except Exception as e:
        print(f"❌ 错误场景测试失败: {e}")
        return False
    
    return True

def test_import_scenarios():
    """测试导入场景"""
    print("\n" + "="*60)
    print("🧪 测试场景3: 导入语句测试")
    print("="*60)
    
    # 测试原始有问题的代码逻辑
    print("🔍 测试原始有问题的代码逻辑...")
    
    try:
        # 模拟原始的错误代码
        image_enabled = True
        img_buffer = None  # 模拟图片生成失败
        
        if image_enabled:
            print("✅ 图片功能已启用")
            
            # 原始错误的代码逻辑
            if img_buffer:  # 这个条件为False
                from datetime import datetime as old_datetime  # 这行不会执行
                print("❌ 这行不应该执行")
            
            # 尝试使用未导入的变量（这会失败）
            try:
                filename = f"test_{old_datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg"
                print("❌ 意外：应该失败但没有失败")
            except NameError as e:
                print(f"✅ 正确捕获到原始问题: {e}")
        
        print("\n🔧 测试修复后的代码逻辑...")
        
        # 修复后的代码逻辑
        if image_enabled:
            print("✅ 图片功能已启用")
            
            # 修复后：提前导入
            from datetime import datetime
            print("✅ 提前导入datetime成功")
            
            if img_buffer:  # 这个条件为False
                print("❌ 这行不应该执行")
            
            # 现在可以安全使用datetime
            filename = f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg"
            print(f"✅ 修复后代码正常工作: {filename}")
            
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("🎯 持续告警图片生成功能独立测试")
    print("="*60)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 Python版本: {sys.version}")
    print(f"📁 当前目录: {os.getcwd()}")
    
    # 检查PIL库
    try:
        import PIL
        print(f"✅ PIL库已安装，版本: {PIL.__version__}")
    except ImportError:
        print("⚠️ PIL库未安装，图片生成功能将失败")
    
    print("\n🚀 开始测试...")
    
    # 执行各种测试
    test_results = []
    
    try:
        test_image_generation()
        test_results.append("图片生成测试: ✅")
    except Exception as e:
        print(f"❌ 图片生成测试失败: {e}")
        test_results.append("图片生成测试: ❌")
    
    try:
        if test_error_scenarios():
            test_results.append("错误场景测试: ✅")
        else:
            test_results.append("错误场景测试: ❌")
    except Exception as e:
        print(f"❌ 错误场景测试异常: {e}")
        test_results.append("错误场景测试: ❌")
    
    try:
        if test_import_scenarios():
            test_results.append("导入场景测试: ✅")
        else:
            test_results.append("导入场景测试: ❌")
    except Exception as e:
        print(f"❌ 导入场景测试异常: {e}")
        test_results.append("导入场景测试: ❌")
    
    # 输出测试结果
    print("\n" + "="*60)
    print("📊 测试结果汇总")
    print("="*60)
    
    for result in test_results:
        print(f"  {result}")
    
    success_count = sum(1 for r in test_results if "✅" in r)
    total_count = len(test_results)
    
    print(f"\n🎯 测试完成: {success_count}/{total_count} 通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！持续告警图片生成功能正常。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
