#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试Unicode乱码加密问题
"""

def debug_unicode_conversion():
    """调试Unicode转换问题"""
    print("🔍 调试Unicode转换")
    print("=" * 50)
    
    # 测试简单的字节数组
    test_bytes = b"Hello World"
    print(f"原始字节: {test_bytes}")
    print(f"字节长度: {len(test_bytes)}")
    
    # 符号范围
    symbol_ranges = [
        (0x2600, 0x267F),  # 杂项符号
        (0x2700, 0x27BF),  # 装饰符号
        (0x25A0, 0x25FF),  # 几何图形
        (0x2190, 0x21FF),  # 箭头
        (0x2660, 0x26FF),  # 杂项符号补充
    ]
    
    # 编码
    print("\n🔐 编码过程:")
    chaos_result = ""
    for i, byte_val in enumerate(test_bytes):
        range_idx = i % len(symbol_ranges)
        start, end = symbol_ranges[range_idx]
        symbol_range_size = end - start
        symbol_code = start + (byte_val % symbol_range_size)
        
        try:
            char = chr(symbol_code)
            char.encode('utf-8')  # 验证可编码
            chaos_result += char
            print(f"  字节 {byte_val:3d} -> 符号 {symbol_code:5d} ({char}) [范围{range_idx}]")
        except (ValueError, UnicodeEncodeError) as e:
            safe_code = 0x2600 + (byte_val % 128)
            char = chr(safe_code)
            chaos_result += char
            print(f"  字节 {byte_val:3d} -> 安全符号 {safe_code:5d} ({char}) [备用] 错误: {e}")
    
    print(f"\n乱码结果: {chaos_result}")
    print(f"乱码长度: {len(chaos_result)}")
    
    # 解码
    print("\n🔓 解码过程:")
    decoded_bytes = bytearray()
    for i, char in enumerate(chaos_result):
        char_code = ord(char)
        range_idx = i % len(symbol_ranges)
        start, end = symbol_ranges[range_idx]
        
        if start <= char_code < end:
            byte_val = char_code - start
            print(f"  符号 {char} ({char_code:5d}) -> 字节 {byte_val:3d} [范围{range_idx}]")
        else:
            # 备用解码
            if 0x2600 <= char_code <= 0x2600 + 127:
                byte_val = char_code - 0x2600
                print(f"  符号 {char} ({char_code:5d}) -> 字节 {byte_val:3d} [备用]")
            else:
                byte_val = char_code % 256
                print(f"  符号 {char} ({char_code:5d}) -> 字节 {byte_val:3d} [模运算]")
        
        decoded_bytes.append(byte_val % 256)
    
    decoded_result = bytes(decoded_bytes)
    print(f"\n解码结果: {decoded_result}")
    print(f"解码长度: {len(decoded_result)}")
    
    # 验证
    if decoded_result == test_bytes:
        print("✅ 编码解码成功！")
        return True
    else:
        print("❌ 编码解码失败！")
        print(f"原始: {list(test_bytes)}")
        print(f"解码: {list(decoded_result)}")
        return False

def debug_encryption_flow():
    """调试完整的加密流程"""
    print("\n🔍 调试完整加密流程")
    print("=" * 50)
    
    try:
        from cryptography.fernet import Fernet
        from cryptography.hazmat.primitives import hashes
        from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
        import base64
    except ImportError:
        print("❌ 加密库不可用")
        return False
    
    # 测试数据
    content = "Hello World 测试"
    password = "test123"
    
    print(f"原始内容: {content}")
    print(f"密码: {password}")
    
    # 生成密钥
    salt = b'alarm_monitor_salt_2024'
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )
    key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
    print(f"生成的密钥: {key[:20]}...")
    
    # 加密
    f = Fernet(key)
    encrypted_bytes = f.encrypt(content.encode('utf-8'))
    print(f"加密字节长度: {len(encrypted_bytes)}")
    print(f"加密字节前20个: {list(encrypted_bytes[:20])}")
    
    # 转换为Unicode乱码
    symbol_ranges = [
        (0x2600, 0x267F),
        (0x2700, 0x27BF),
        (0x25A0, 0x25FF),
        (0x2190, 0x21FF),
        (0x2660, 0x26FF),
    ]
    
    chaos_result = ""
    for i, byte_val in enumerate(encrypted_bytes):
        range_idx = i % len(symbol_ranges)
        start, end = symbol_ranges[range_idx]
        symbol_range_size = end - start
        symbol_code = start + (byte_val % symbol_range_size)
        
        try:
            char = chr(symbol_code)
            char.encode('utf-8')
            chaos_result += char
        except (ValueError, UnicodeEncodeError):
            safe_code = 0x2600 + (byte_val % 128)
            chaos_result += chr(safe_code)
    
    print(f"Unicode乱码长度: {len(chaos_result)}")
    print(f"Unicode乱码前10个: {chaos_result[:10]}")
    
    # 从Unicode乱码恢复字节
    recovered_bytes = bytearray()
    for i, char in enumerate(chaos_result):
        char_code = ord(char)
        range_idx = i % len(symbol_ranges)
        start, end = symbol_ranges[range_idx]
        
        if start <= char_code < end:
            byte_val = char_code - start
        else:
            if 0x2600 <= char_code <= 0x2600 + 127:
                byte_val = char_code - 0x2600
            else:
                byte_val = char_code % 256
        
        recovered_bytes.append(byte_val % 256)
    
    recovered_bytes = bytes(recovered_bytes)
    print(f"恢复字节长度: {len(recovered_bytes)}")
    
    # 验证字节是否一致
    if recovered_bytes == encrypted_bytes:
        print("✅ Unicode转换成功")
    else:
        print("❌ Unicode转换失败")
        print(f"原始前10个: {list(encrypted_bytes[:10])}")
        print(f"恢复前10个: {list(recovered_bytes[:10])}")
        return False
    
    # 解密
    try:
        decrypted_bytes = f.decrypt(recovered_bytes)
        decrypted_content = decrypted_bytes.decode('utf-8')
        print(f"解密结果: {decrypted_content}")
        
        if decrypted_content == content:
            print("✅ 完整流程成功！")
            return True
        else:
            print("❌ 解密内容不匹配")
            return False
    except Exception as e:
        print(f"❌ 解密失败: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Unicode乱码加密调试")
    print("=" * 60)
    
    # 测试基础转换
    test1 = debug_unicode_conversion()
    
    # 测试完整流程
    test2 = debug_encryption_flow()
    
    print("\n" + "=" * 60)
    if test1 and test2:
        print("🎉 所有调试测试通过！")
    else:
        print("❌ 调试发现问题")
    print("=" * 60)
