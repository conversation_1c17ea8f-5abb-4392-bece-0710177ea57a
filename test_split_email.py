#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试拆分邮件功能
验证每个告警组是否能独立发送邮件
"""

import sys
import os
from datetime import datetime

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_split_email_logic():
    """测试拆分邮件的核心逻辑"""
    print("🧪 测试拆分邮件功能...")
    
    # 模拟告警数据
    mock_alarms = [
        {
            'alarm_key': 'alarm_001',
            'code_name': '小区退服告警',
            'me_name': '基站001',
            'ne_ip': '***********',
            'perceived_severity_name': '严重',
            'alarm_raised_time': '2024-01-15 10:30:00',
            'effective_duration_minutes': 120,
            'raw_data': {'alarmkey': 'alarm_001'}
        },
        {
            'alarm_key': 'alarm_002', 
            'code_name': '天馈驻波比异常',
            'me_name': '基站002',
            'ne_ip': '***********',
            'perceived_severity_name': '重要',
            'alarm_raised_time': '2024-01-15 11:00:00',
            'effective_duration_minutes': 60,
            'raw_data': {'alarmkey': 'alarm_002'}
        },
        {
            'alarm_key': 'alarm_003',
            'code_name': '光模块接收光功率异常', 
            'me_name': '基站003',
            'ne_ip': '***********',
            'perceived_severity_name': '一般',
            'alarm_raised_time': '2024-01-15 11:30:00',
            'effective_duration_minutes': 30,
            'raw_data': {'alarmkey': 'alarm_003'}
        }
    ]
    
    # 模拟分组逻辑（简化版）
    groups = {}
    for alarm in mock_alarms:
        # 使用告警名称作为分组键（实际项目中是根源ID）
        group_key = alarm['code_name'][:10]  # 取前10个字符作为组键
        if group_key not in groups:
            groups[group_key] = []
        groups[group_key].append(alarm)
    
    print(f"📊 模拟数据: {len(mock_alarms)} 条告警，分为 {len(groups)} 组")
    for group_key, group_alarms in groups.items():
        print(f"  组 '{group_key}': {len(group_alarms)} 条告警")
    
    # 测试邮件内容生成
    print("\n📧 测试邮件内容生成...")
    
    for group_key, group_alarms in groups.items():
        print(f"\n--- 组 '{group_key}' 的邮件内容 ---")
        content = generate_test_email_content(group_key, group_alarms)
        print(content[:200] + "..." if len(content) > 200 else content)
    
    print("\n✅ 拆分邮件逻辑测试完成")
    return True

def generate_test_email_content(group_key, group_alarms):
    """生成测试邮件内容（简化版）"""
    import uuid
    
    content_lines = []
    content_lines.append(f"告警监控系统 - 独立告警通知")
    content_lines.append(f"发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    content_lines.append(f"告警组: {group_key}")
    content_lines.append(f"告警数量: {len(group_alarms)} 条")
    content_lines.append("")
    content_lines.append("=" * 40)
    content_lines.append("")
    
    # 添加告警详情
    for i, alarm in enumerate(group_alarms, 1):
        content_lines.append(f"告警 {i}:")
        content_lines.append(f"  告警名称: {alarm.get('code_name', '未知')}")
        content_lines.append(f"  网元名称: {alarm.get('me_name', '未知')}")
        content_lines.append(f"  IP地址: {alarm.get('ne_ip', '未知')}")
        content_lines.append(f"  告警级别: {alarm.get('perceived_severity_name', '未知')}")
        content_lines.append(f"  发生时间: {alarm.get('alarm_raised_time', '未知')}")
        
        # 添加持续时间信息
        duration_minutes = alarm.get('effective_duration_minutes', 0)
        if duration_minutes > 0:
            hours = duration_minutes // 60
            mins = duration_minutes % 60
            if hours > 0:
                duration_text = f"{hours}小时{mins}分钟" if mins > 0 else f"{hours}小时"
            else:
                duration_text = f"{mins}分钟"
            content_lines.append(f"  持续时间: {duration_text}")
        
        content_lines.append("")
    
    content_lines.append("=" * 40)
    content_lines.append("")
    content_lines.append("请及时处理相关告警。")
    content_lines.append("")
    content_lines.append(f"<!-- UUID: {str(uuid.uuid4())} -->")
    
    return "\n".join(content_lines)

def test_email_sending_simulation():
    """模拟邮件发送过程"""
    print("\n🚀 模拟邮件发送过程...")
    
    # 模拟邮件配置
    mock_settings = {
        'smtp_host': 'smtp.qq.com',
        'smtp_port': 465,
        'use_ssl': True,
        'username': '<EMAIL>',
        'password': 'test_password',
        'from_addr': '<EMAIL>',
        'to_addr': '<EMAIL>'
    }
    
    # 模拟3组告警
    mock_groups = {
        'group_001': [{'alarm_key': 'alarm_001', 'code_name': '告警1'}],
        'group_002': [{'alarm_key': 'alarm_002', 'code_name': '告警2'}, 
                      {'alarm_key': 'alarm_003', 'code_name': '告警3'}],
        'group_003': [{'alarm_key': 'alarm_004', 'code_name': '告警4'}]
    }
    
    total_groups = len(mock_groups)
    success_count = 0
    failed_count = 0
    
    print(f"📧 开始模拟发送 {total_groups} 组告警邮件...")
    
    for group_index, (group_key, group_alarms) in enumerate(mock_groups.items(), 1):
        print(f"📧 正在模拟发送第 {group_index}/{total_groups} 组邮件 (组ID: {group_key})")
        
        # 模拟邮件发送（实际中会调用SMTP）
        try:
            # 这里只是模拟，实际发送会调用 smtplib
            import time
            time.sleep(0.1)  # 模拟发送时间
            
            print(f"✅ 第 {group_index} 组邮件发送成功 - {len(group_alarms)} 条告警")
            success_count += 1
            
            # 模拟发送间隔
            if group_index < total_groups:
                print(f"⏱️ 等待 2 秒后发送下一组...")
                time.sleep(0.2)  # 模拟中缩短等待时间
                
        except Exception as e:
            print(f"❌ 第 {group_index} 组邮件发送失败: {e}")
            failed_count += 1
    
    print(f"\n📧 邮件发送完成: 成功 {success_count} 组, 失败 {failed_count} 组")
    return success_count, failed_count

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 告警邮件拆分功能测试")
    print("=" * 60)
    
    try:
        # 测试拆分逻辑
        test_split_email_logic()
        
        # 测试发送模拟
        success, failed = test_email_sending_simulation()
        
        print("\n" + "=" * 60)
        if failed == 0:
            print("🎉 所有测试通过！拆分邮件功能正常")
        else:
            print(f"⚠️ 测试完成，但有 {failed} 个失败")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
