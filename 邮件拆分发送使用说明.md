# 邮件拆分发送功能使用说明

## 功能概述

告警监控系统现在支持两种邮件发送模式：

1. **拆分发送** - 每个告警组发送一封独立邮件
2. **合并发送** - 所有告警合并为一封邮件（原有模式）

## 配置方法

### 1. 通过配置文件设置

编辑 `monitor_config.ini` 文件，在 `[email]` 部分添加：

```ini
[email]
enabled = true
smtp_host = smtp.qq.com
smtp_port = 465
use_ssl = true
username = <EMAIL>
password = your_password
from_addr = <EMAIL>
to_addr = <EMAIL>
send_mode = split    # 拆分发送模式
```

**send_mode 可选值：**
- `split` - 拆分发送（推荐）
- `merged` - 合并发送

### 2. 通过GUI界面设置

1. 启动告警监控程序
2. 点击"邮件设置"按钮
3. 在"📨 邮件发送模式"部分选择：
   - "拆分发送 - 每组告警独立邮件"
   - "合并发送 - 所有告警一封邮件"
4. 点击"保存"按钮

## 两种模式对比

### 拆分发送模式（推荐）

**优点：**
- ✅ 每个告警组独立邮件，便于跟踪和处理
- ✅ 重要告警不会被淹没在长邮件中
- ✅ 支持按告警组分别处理
- ✅ 邮件大小可控，不会被邮件系统拒收
- ✅ 便于邮件过滤和分类

**特点：**
- 每组告警发送一封独立邮件
- 邮件间隔2秒，避免邮件服务器限制
- 每封邮件包含该组的所有告警详情
- 邮件主题包含告警组信息

**示例输出：**
```
📧 检测到 5 条新告警，分为 3 组，将分别发送独立邮件
📧 正在发送第 1/3 组告警邮件 (根源ID: abc12345...)
✅ 第 1 组邮件发送成功 (1.2秒) - 2 条告警
📧 正在发送第 2/3 组告警邮件 (根源ID: def67890...)
✅ 第 2 组邮件发送成功 (0.8秒) - 2 条告警
📧 正在发送第 3/3 组告警邮件 (根源ID: ghi11111...)
✅ 第 3 组邮件发送成功 (0.9秒) - 1 条告警
📧 邮件发送完成: 成功 3 组, 失败 0 组
```

### 合并发送模式

**优点：**
- ✅ 邮件数量少，减少邮箱占用
- ✅ 可以看到所有告警的全貌
- ✅ 包含Excel附件，便于数据分析

**缺点：**
- ❌ 重要告警可能被淹没
- ❌ 邮件过大可能被拒收
- ❌ 不便于按告警分别处理

**示例输出：**
```
📧 检测到 5 条新告警，分为 3 组，合并为一封邮件发送
📧 正在发送包含 5 条告警的合并邮件...
📧 合并邮件发送成功 (2.1秒)
📧 总计发送成功: 1封告警邮件(共5条，3组)
```

## 邮件内容示例

### 拆分发送邮件内容

```
主题: [告警通知] 小区退服告警 等 2 条告警 - 01-15 10:30

告警监控系统 - 独立告警通知
发送时间: 2024-01-15 10:30:15
根源分组ID: abc12345-def6-7890-abcd-ef1234567890
告警数量: 2 条

============================================================

告警 1:
  告警名称: 小区退服告警
  网元名称: 基站001
  IP地址: ***********
  告警级别: 严重
  发生时间: 2024-01-15 10:30:00
  持续时间: 2小时

告警 2:
  告警名称: 天馈驻波比异常
  网元名称: 基站001
  IP地址: ***********
  告警级别: 重要
  发生时间: 2024-01-15 10:35:00
  持续时间: 1小时55分钟

============================================================

请及时处理相关告警。
```

## 技术实现

### 核心改进

1. **模块化设计**
   - `send_individual_emails()` - 拆分发送逻辑
   - `send_merged_email()` - 合并发送逻辑
   - `get_email_send_mode()` - 配置读取

2. **智能发送控制**
   - 2秒发送间隔，避免邮件服务器限制
   - 独立错误处理，单个邮件失败不影响其他
   - 详细的发送进度日志

3. **配置兼容性**
   - 默认使用拆分发送模式
   - 向后兼容原有配置
   - 支持运行时配置切换

## 故障排除

### 常见问题

**Q: 拆分发送时某些邮件发送失败怎么办？**
A: 系统会继续发送其他邮件，失败的邮件会在日志中显示具体错误信息。

**Q: 拆分发送会不会被邮件服务器限制？**
A: 系统设置了2秒发送间隔，一般不会触发限制。如果仍有问题，可以临时切换到合并发送模式。

**Q: 如何快速切换发送模式？**
A: 可以直接修改配置文件中的 `send_mode` 值，或通过GUI界面修改。

### 日志分析

拆分发送的日志格式：
```
📧 检测到 X 条新告警，分为 Y 组，将分别发送独立邮件
📧 正在发送第 N/Y 组告警邮件 (根源ID: ...)
✅ 第 N 组邮件发送成功 (X.X秒) - X 条告警
📧 邮件发送完成: 成功 X 组, 失败 X 组
```

## 建议使用场景

### 推荐使用拆分发送：
- 告警数量较多（>5条）
- 需要按告警组分别处理
- 有多个运维人员协作
- 需要邮件过滤和分类

### 推荐使用合并发送：
- 告警数量较少（<5条）
- 需要整体分析告警情况
- 邮箱空间有限
- 习惯使用Excel附件分析

## 更新日志

- **v2.1** - 新增拆分发送功能
- **v2.0** - 原有合并发送功能
