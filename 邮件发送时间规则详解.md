# 邮件发送时间规则详解

## 🎯 **Linus式邮件发送规则分析**

**"让我详细解释程序的邮件发送时间规则。"**

## 📊 **邮件发送触发时机**

### **1. 数据刷新时触发**
```python
# 每次数据刷新后都会检查是否需要发送邮件
def load_data(self):
    # ... 数据加载逻辑 ...
    
    # 邮件发送检查
    if is_first_baseline:
        self.add_log("📧 跳过邮件发送（全部为历史告警）")
    else:
        # 发送新告警邮件
        self.maybe_send_email(alarms)
        # 发送持续告警邮件
        self.maybe_send_sustained_emails(alarms)
```

### **2. 触发条件**
- ✅ **有新告警**：`is_new = 1` 的告警
- ✅ **非全部历史告警**：不是首次加载且全部为基线告警
- ✅ **邮件功能已启用**：配置文件中 `enabled = true`

## 📧 **新告警邮件规则**

### **发送条件**
```python
def build_email_candidates(self, alarms):
    candidates = []
    for a in alarms:
        # 必须同时满足以下条件：
        if (a.get('is_active', 1) == 1 and          # 活跃告警
            a.get('is_new', 0) == 1 and             # 新告警
            a.get('is_baseline', 0) == 0):          # 非基线告警
            
            # 运营商过滤：只发送联通告警
            if '联通' in str(a.get('operator_info', '')):
                
                # 关联性过滤：重点告警或关联告警
                is_focus = any(kw in (a.get('code_name', '') or '') for kw in self.focus_keywords)
                is_related = a.get('relationflag', 0) in (1, 2, 3)
                
                if is_focus or is_related:
                    candidates.append(a)
    
    return candidates
```

### **发送频率**
- **立即发送**：检测到新告警后立即发送
- **去重机制**：已发送的告警不会重复发送（基于 `sent_email_keys` 集合）
- **分组发送**：按 `root_group_id` 前6位分组，每组发送一封邮件
- **发送间隔**：组与组之间间隔2秒，避免被识别为垃圾邮件

## ⏰ **持续告警邮件规则**

### **阈值设置**
```python
# 持续阈值（分钟）
SUSTAINED_THRESHOLDS_MINUTES = [60, 120, 180, 240, 480, 1440, 2880, 4320]
# 对应：1小时, 2小时, 3小时, 4小时, 8小时, 1天, 2天, 3天
```

### **发送条件**
```python
def maybe_send_sustained_emails(self, alarms):
    for a in alarms:
        # 基础条件
        if not (a.get('is_active', 1) == 1 and a.get('is_new', 0) == 0):
            continue  # 必须是活跃的非新告警
        
        # 运营商过滤
        is_unicom = '联通' in str(a.get('operator_info', ''))
        if not is_unicom:
            continue
        
        # 重点告警或关联告警
        is_focus = any(kw in (a.get('code_name', '') or '') for kw in self.focus_keywords)
        is_related = a.get('relationflag', 0) in (1, 2, 3)
        if not (is_focus or is_related):
            continue
        
        # 计算持续时间
        minutes = self._get_effective_minutes_for_alarm(a)
        
        # 超过5天的告警不发送
        if minutes >= 7200:  # 5天 = 7200分钟
            continue
        
        # 检查是否跨越阈值
        crossed = [t for t in SUSTAINED_THRESHOLDS_MINUTES if minutes >= t]
        if crossed:
            max_threshold = max(crossed)
            # 检查是否需要发送（比上次发送的阈值更高）
            last_sent = self._get_group_last_threshold(threshold_key)
            if max_threshold > last_sent:
                # 需要发送
                pass
```

### **发送频率**
- **阈值触发**：只有当告警跨越新的时间阈值时才发送
- **去重机制**：每个告警的每个阈值只发送一次
- **持久化记录**：在数据库中记录已发送的阈值，重启后仍有效

## 🕐 **时间计算规则**

### **考核时间计算**
```python
def calculate_effective_duration(start_time, end_time):
    """
    计算考核持续时间（排除每天0:00-5:59的非考核时间段）
    考核时间：每天 6:00-23:59
    非考核时间：每天 0:00-5:59
    """
    # 只计算 6:00-23:59 时间段内的持续时间
    # 0:00-5:59 时间段不计入考核时间
```

### **时间阈值对应**
| 阈值(分钟) | 时间 | 说明 |
|------------|------|------|
| 60 | 1小时 | 短期持续 |
| 120 | 2小时 | 中期持续 |
| 180 | 3小时 | 较长持续 |
| 240 | 4小时 | 长期持续 |
| 480 | 8小时 | 严重持续 |
| 1440 | 1天 | 极严重持续 |
| 2880 | 2天 | 超长持续 |
| 4320 | 3天 | 最长持续 |

## 📋 **邮件发送流程**

### **新告警邮件流程**
```text
1. 数据刷新 → 检测新告警
2. 过滤条件：活跃 + 新告警 + 非基线 + 联通 + (重点或关联)
3. 按root_group_id分组
4. 每组生成一封邮件
5. 逐组发送，间隔2秒
6. 记录已发送告警，避免重复
```

### **持续告警邮件流程**
```text
1. 数据刷新 → 检测持续告警
2. 过滤条件：活跃 + 非新告警 + 联通 + (重点或关联)
3. 计算考核持续时间
4. 检查是否跨越新阈值
5. 与历史发送记录对比
6. 按组发送超过新阈值的告警
7. 更新数据库发送记录
```

## ⚙️ **配置控制**

### **邮件开关**
```ini
[email]
enabled = true  # 启用/禁用邮件功能
```

### **重点关键字**
```ini
[focus]
keywords = 小区退
天馈驻波比异常
光模块接收光功率异常
光口接收链路故障
```

### **定时获取**
```ini
[timer]
enabled = false        # 是否启用定时获取
interval_minutes = 10  # 获取间隔（分钟）
```

## 🎯 **Linus式总结**

**"邮件发送规则设计得很合理，避免了垃圾邮件的同时确保重要告警不会遗漏。"**

### **核心特点**
1. **精确过滤**：只发送联通的重点/关联告警
2. **智能去重**：避免重复发送相同告警
3. **阶梯阈值**：持续告警按时间阶梯发送
4. **考核时间**：排除非考核时间段，更准确
5. **分组发送**：相关告警合并发送，便于处理

### **发送时机**
- **新告警**：立即发送（数据刷新时）
- **持续告警**：跨越阈值时发送
- **频率控制**：组间间隔2秒，避免限制

### **过滤条件**
- ✅ **运营商**：只发送联通告警
- ✅ **重要性**：重点告警或关联告警
- ✅ **状态**：活跃且符合新/持续条件
- ✅ **时间**：持续告警不超过5天

**"这个设计确保了邮件的及时性和准确性，既不会漏报重要告警，也不会产生垃圾邮件。"** 🎯

---

**文档时间**: 2024-08-18  
**规则版本**: 当前版本  
**适用范围**: 新告警和持续告警邮件
