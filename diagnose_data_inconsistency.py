#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断程序界面与邮件数据不一致问题
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def analyze_data_flow():
    """分析数据流程"""
    print("🔍 程序界面与邮件数据不一致问题分析")
    print("=" * 60)
    
    print("📊 数据流程分析:")
    print("1. 数据库查询 → 获取原始告警数据")
    print("2. 关联分析 → 生成correlation_info")
    print("3. 数据处理 → 生成告警对象")
    print("4. 界面显示 → 使用告警对象")
    print("5. 邮件发送 → 使用告警对象")
    
    print("\n🎯 关键发现:")
    print("程序界面和邮件发送使用相同的数据源（告警对象）")
    print("但是有两个不同的字段：")
    print("- 界面显示：alarm.get('root_group_id_short', '')")
    print("- 邮件分组：alarm.get('root_group_id', '')")
    
    return True

def analyze_field_generation():
    """分析字段生成逻辑"""
    print("\n🔍 字段生成逻辑分析")
    print("=" * 60)
    
    print("📋 root_group_id 生成逻辑:")
    print("```python")
    print("root_group_id = ''")
    print("if relationflag == 1:")
    print("    root_group_id = str(row[20])  # 根源告警：自身alarm_key")
    print("else:")
    print("    root_group_id = correlation_info.get('root_alarmkey', '')  # 衍生/次根源：追根结果")
    print("```")
    
    print("\n📋 root_group_id_short 生成逻辑:")
    print("```python")
    print("'root_group_id_short': (root_group_id[:8] + '...') if root_group_id and len(root_group_id) > 8 else root_group_id")
    print("```")
    
    print("\n🎯 关键点:")
    print("1. root_group_id_short 是基于 root_group_id 生成的")
    print("2. 如果 root_group_id 为空，root_group_id_short 也为空")
    print("3. 如果界面显示有值，说明 root_group_id 不为空")
    print("4. 如果邮件分组错误，说明邮件使用的数据有问题")
    
    return True

def analyze_possible_causes():
    """分析可能的原因"""
    print("\n🔍 可能原因分析")
    print("=" * 60)
    
    print("❓ 如果界面显示正确，邮件分组错误，可能的原因：")
    
    print("\n1. 🔄 数据时间差问题")
    print("   - 界面显示的是最新数据")
    print("   - 邮件发送时使用了旧数据")
    print("   - 数据在处理过程中被修改")
    
    print("\n2. 🔄 数据处理顺序问题")
    print("   - 界面更新在关联分析之后")
    print("   - 邮件发送在关联分析之前")
    print("   - 关联分析结果没有及时更新到邮件数据")
    
    print("\n3. 🔄 数据拷贝问题")
    print("   - 界面和邮件使用了不同的数据副本")
    print("   - 数据在传递过程中丢失")
    print("   - 引用vs拷贝的问题")
    
    print("\n4. 🔄 缓存问题")
    print("   - 邮件发送使用了缓存的旧数据")
    print("   - 界面显示使用了最新数据")
    print("   - 缓存没有及时更新")
    
    return True

def analyze_code_flow():
    """分析代码执行流程"""
    print("\n🔍 代码执行流程分析")
    print("=" * 60)
    
    print("📋 程序执行顺序:")
    print("1. get_alarms() → 获取数据库数据")
    print("2. analyze_alarm_correlations() → 关联分析")
    print("3. 生成告警对象（包含root_group_id和root_group_id_short）")
    print("4. update_table_with_smart_pagination() → 更新界面")
    print("5. send_new_alarm_emails() → 发送邮件")
    
    print("\n🎯 关键时间点:")
    print("- 界面更新：第4步")
    print("- 邮件发送：第5步")
    print("- 数据应该是一致的")
    
    print("\n❓ 可能的问题点:")
    print("1. 第3步到第4步之间：数据被修改？")
    print("2. 第4步到第5步之间：数据被修改？")
    print("3. 第5步内部：使用了不同的数据源？")
    
    return True

def suggest_debugging_approach():
    """建议调试方法"""
    print("\n🔧 调试方法建议")
    print("=" * 60)
    
    print("📋 立即验证步骤:")
    print("1. 🔍 检查界面显示的42d731告警")
    print("   - 查看'根源ID（短）'列的值")
    print("   - 如果有值，记录下来")
    
    print("\n2. 🔍 检查程序日志")
    print("   - 搜索'🔍 调试42d731分组'")
    print("   - 查看root_group_id的实际值")
    print("   - 对比界面显示的值")
    
    print("\n3. 🔍 添加更多调试信息")
    debug_code = '''
# 在邮件发送前添加调试代码
for group_key, alarms in groups.items():
    if '42d731' in group_key:
        print(f"🔍 邮件发送调试 - 分组: {group_key}")
        for alarm in alarms:
            print(f"  告警: {alarm.get('code_name', '未知')}")
            print(f"  root_group_id: '{alarm.get('root_group_id', '')}'")
            print(f"  root_group_id_short: '{alarm.get('root_group_id_short', '')}'")
            print(f"  alarm_key: {alarm.get('alarm_key', '')}")
'''
    
    print("\n💻 建议添加的调试代码:")
    print(debug_code)
    
    print("\n📋 深度分析步骤:")
    print("1. 对比界面显示值和日志输出值")
    print("2. 检查数据是否在处理过程中被修改")
    print("3. 验证邮件发送时的数据完整性")
    print("4. 检查是否有异步处理导致的时序问题")
    
    return True

def create_verification_checklist():
    """创建验证清单"""
    print("\n📋 验证清单")
    print("=" * 60)
    
    checklist = [
        "[ ] 界面显示：42d731的'根源ID（短）'列是否有值？",
        "[ ] 程序日志：搜索'🔍 调试42d731分组'是否有输出？",
        "[ ] 日志对比：root_group_id值是否与界面显示一致？",
        "[ ] 邮件分组：42d731是否被分组为'独立_42d731'？",
        "[ ] 同组告警：是否有其他告警应该与42d731同组？",
        "[ ] 数据一致性：同组告警的root_group_id是否相同？",
        "[ ] 时序问题：界面更新和邮件发送的时间顺序？",
        "[ ] 缓存问题：是否存在数据缓存不一致？"
    ]
    
    print("🔍 请逐项检查以下内容:")
    for item in checklist:
        print(f"  {item}")
    
    return True

if __name__ == "__main__":
    print("🔍 程序界面与邮件数据不一致问题诊断")
    print("=" * 80)
    
    try:
        # 分析数据流程
        test1 = analyze_data_flow()
        
        # 分析字段生成逻辑
        test2 = analyze_field_generation()
        
        # 分析可能原因
        test3 = analyze_possible_causes()
        
        # 分析代码流程
        test4 = analyze_code_flow()
        
        # 建议调试方法
        test5 = suggest_debugging_approach()
        
        # 创建验证清单
        test6 = create_verification_checklist()
        
        print("\n" + "=" * 80)
        if all([test1, test2, test3, test4, test5, test6]):
            print("🎯 诊断总结:")
            print("\n关键发现：")
            print("✅ 界面和邮件使用相同的数据源")
            print("✅ root_group_id_short 基于 root_group_id 生成")
            print("❓ 如果界面显示正确，邮件分组错误，说明存在数据不一致")
            print("\n可能原因：")
            print("1. 数据在处理过程中被修改")
            print("2. 时序问题导致数据不同步")
            print("3. 缓存或引用问题")
            print("4. 异步处理导致的竞态条件")
            print("\n下一步：")
            print("1. 检查界面显示的42d731根源ID值")
            print("2. 查看程序日志中的调试信息")
            print("3. 对比两者是否一致")
            print("4. 根据结果确定具体问题")
        else:
            print("⚠️ 诊断过程中出现问题")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        import traceback
        traceback.print_exc()
