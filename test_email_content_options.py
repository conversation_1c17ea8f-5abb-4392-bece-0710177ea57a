#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试邮件内容选项功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_email_content_options():
    """测试邮件内容选项功能"""
    try:
        # 导入主模块
        from alarm_monitor_pyside6 import EMAIL_CONTENT_OPTIONS, EMAIL_DEFAULTS, EmailConfigDialog
        
        print("=" * 60)
        print("邮件内容选项功能测试")
        print("=" * 60)
        
        # 测试1：检查邮件内容选项定义
        print("1. 邮件内容选项定义:")
        for key, description in EMAIL_CONTENT_OPTIONS.items():
            print(f"   {key}: {description}")
        
        print(f"\n   总计 {len(EMAIL_CONTENT_OPTIONS)} 种内容选项")
        
        # 测试2：检查默认配置
        print(f"\n2. 默认配置:")
        print(f"   默认内容类型: {EMAIL_DEFAULTS.get('content_type', '未设置')}")
        
        # 测试3：检查配置文件
        print(f"\n3. 配置文件检查:")
        import configparser
        config_file = "monitor_config.ini"
        if os.path.exists(config_file):
            cfg = configparser.ConfigParser()
            cfg.read(config_file, encoding='utf-8')
            if cfg.has_section('email'):
                content_type = cfg.get('email', 'content_type', fallback='未设置')
                print(f"   配置文件中的内容类型: {content_type}")
                if content_type in EMAIL_CONTENT_OPTIONS:
                    print(f"   对应描述: {EMAIL_CONTENT_OPTIONS[content_type]}")
                else:
                    print(f"   ⚠️ 配置值无效")
            else:
                print("   配置文件中没有email段")
        else:
            print("   配置文件不存在")
        
        # 测试4：模拟不同内容类型的处理逻辑
        print(f"\n4. 内容类型处理逻辑测试:")
        test_cases = [
            'all', 'text_only', 'image_only', 'excel_only', 
            'text_image', 'text_excel', 'image_excel'
        ]
        
        for content_type in test_cases:
            needs_image = content_type in ['all', 'image_only', 'text_image', 'image_excel']
            needs_excel = content_type in ['all', 'excel_only', 'text_excel', 'image_excel']
            needs_text = content_type in ['all', 'text_only', 'text_image', 'text_excel']
            
            components = []
            if needs_text:
                components.append("文本")
            if needs_image:
                components.append("图片")
            if needs_excel:
                components.append("Excel")
            
            print(f"   {content_type:12} -> {' + '.join(components) if components else '无内容'}")
        
        print("\n" + "=" * 60)
        print("✅ 邮件内容选项功能测试通过")
        print("新增功能:")
        print("1. 添加了7种邮件内容发送选项")
        print("2. 在邮件配置对话框中添加了内容类型选择")
        print("3. 修改了邮件发送逻辑以支持不同内容类型")
        print("4. 添加了相应的辅助邮件生成方法")
        print("5. 更新了配置文件保存和加载逻辑")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_dialog():
    """测试配置对话框（需要GUI环境）"""
    try:
        from PySide6.QtWidgets import QApplication
        from alarm_monitor_pyside6 import EmailConfigDialog
        
        print("\n" + "=" * 60)
        print("配置对话框测试")
        print("=" * 60)
        
        # 创建应用程序实例（如果还没有）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建配置对话框
        dialog = EmailConfigDialog()
        
        # 检查是否有内容类型选择控件
        if hasattr(dialog, 'content_type_combo'):
            print("✅ 内容类型选择控件已添加")
            print(f"   选项数量: {dialog.content_type_combo.count()}")
            
            # 检查所有选项
            for i in range(dialog.content_type_combo.count()):
                text = dialog.content_type_combo.itemText(i)
                data = dialog.content_type_combo.itemData(i)
                print(f"   选项 {i}: {text} (值: {data})")
        else:
            print("❌ 内容类型选择控件未找到")
            return False
        
        print("✅ 配置对话框测试通过")
        return True
        
    except ImportError:
        print("⚠️ PySide6未安装，跳过GUI测试")
        return True
    except Exception as e:
        print(f"配置对话框测试失败: {e}")
        return False

if __name__ == "__main__":
    success1 = test_email_content_options()
    success2 = test_config_dialog()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！邮件内容选项功能已成功实现")
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
    
    sys.exit(0 if (success1 and success2) else 1)
