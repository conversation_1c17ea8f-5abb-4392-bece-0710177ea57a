# 当前邮件内容逻辑和实际示例

## 📧 邮件发送逻辑

### 1. 告警分组规则
```text
按 root_group_id 分组：
- 根源告警：root_group_id = 自身的 alarm_key
- 衍生告警：root_group_id = 追溯到的根源 alarm_key  
- 次根源告警：root_group_id = 追溯到的根源 alarm_key
- 独立告警：root_group_id = 空，按自身 alarm_key 分组
```

### 2. 分组键生成
```python
def _group_key_short(self, alarm):
    root_id = alarm.get('root_group_id', '').strip()
    if not root_id:
        # 独立告警：按 alarm_key 分组
        ak = alarm.get('alarm_key', '')
        return f"独立_{ak[:6]}"  # 取前6位
    return root_id[:6]  # 根源ID的前6位
```

### 3. 邮件发送方式
- **每个分组发送一封独立邮件**
- 同一根源的所有相关告警在一封邮件中

## 📋 实际邮件内容示例

### 示例场景
假设有以下告警：
1. **根源告警**: 小区退服告警 (alarm_key: ROOT_12345678)
2. **衍生告警1**: 天馈驻波比异常 (追溯到: ROOT_12345678)  
3. **衍生告警2**: 光功率异常 (追溯到: ROOT_12345678)
4. **独立告警**: 温度过高告警 (alarm_key: TEMP_87654321)

### 分组结果
```text
分组1: ROOT_1 (根源ID前6位)
├── 小区退服告警 (🔴根源)
├── 天馈驻波比异常 (🟡衍生←小区退服)
└── 光功率异常 (🟡衍生←小区退服)

分组2: 独立_TEMP_8 (独立告警前6位)
└── 温度过高告警 (独立告警)
```

### 邮件1内容 (根源分组)
```text
告警监控系统 - 独立告警通知
发送时间: 2024-08-18 15:30:00
根源分组ID: ROOT_1
告警数量: 3 条

============================================================

告警 1:
  告警名称: 小区退服告警
  位置信息: 杭州市西湖区文三路123号
  网元名称: 基站001-主设备
  发生时间: 2024-08-18 10:30:00
  持续时间: 2小时30分钟
  关联标记: 🔴根源

告警 2:
  告警名称: 天馈驻波比异常
  位置信息: 杭州市西湖区文三路123号
  网元名称: 基站001-天线系统
  发生时间: 2024-08-18 10:35:00
  持续时间: 2小时25分钟
  关联标记: 🟡衍生←小区退服

告警 3:
  告警名称: 光功率异常
  位置信息: 杭州市西湖区文三路123号
  网元名称: 基站001-光模块
  发生时间: 2024-08-18 10:40:00
  持续时间: 2小时20分钟
  关联标记: 🟡衍生←小区退服

============================================================

请及时处理相关告警。

<!-- UUID: 12345678-1234-1234-1234-123456789abc -->
```

### 邮件2内容 (独立告警)
```text
告警监控系统 - 独立告警通知
发送时间: 2024-08-18 15:30:00
根源分组ID: 独立_TEMP_8
告警数量: 1 条

============================================================

告警 1:
  告警名称: 温度过高告警
  位置信息: 杭州市滨江区网商路456号
  网元名称: 机房空调001
  发生时间: 2024-08-18 11:00:00
  持续时间: 1小时30分钟

============================================================

请及时处理相关告警。

<!-- UUID: *************-4321-4321-987654321def -->
```

## 🔐 加密后的邮件内容

### Unicode乱码加密版本
```text
🔐 此邮件内容已加密

-----BEGIN ENCRYPTED CONTENT-----
☙☴★☁☐☔★☁☐☦☽☯☜☃☙☏☒☴☕☧☌☔☹☧☑☴☙☣☝☥☚☛☜☝☞☟☠☡☢☣☤☥☦☧☨☩☪☫☬☭☮☯☰☱☲☳☴☵☶☷☸☹☺☻☼☽☾☿♀☀☁☂☃☄★☆☇☈☉☊☋☌☍☎☏☐☑☒☓☔☕☖☗☘☙☚☛☜☝☞☟☠☡☢☣☤☥☦☧☨☩☪☫☬☭☮☯☰☱☲☳☴☵☶☷☸☹☺☻☼☽☾☿♀☀☁☂☃☄★☆☇☈☉☊☋☌☍☎☏☐☑☒☓☔☕☖☗☘☙☚☛☜☝☞☟☠☡☢☣☤☥☦☧☨☩☪☫☬☭☮☯☰☱☲☳☴☵☶☷☸☹☺☻☼☽☾☿♀
-----END ENCRYPTED CONTENT-----

密码: xjx001515
```

## 📊 邮件主题格式

### 主题生成逻辑
```python
# 邮件主题格式
subject = f"[告警通知] {alarm_name} 等 {alarm_count} 条告警 - {datetime.now().strftime('%m-%d %H:%M')}"
```

### 实际主题示例
```text
邮件1主题: [告警通知] 小区退服告警 等 3 条告警 - 08-18 15:30
邮件2主题: [告警通知] 温度过高告警 等 1 条告警 - 08-18 15:30
```

## 🎯 关键特点

### 1. 分组逻辑
- **智能关联**: 根源告警和其衍生告警在同一封邮件中
- **独立处理**: 无关联的告警单独发送
- **避免混乱**: 不同根源的告警不会混在一起

### 2. 内容完整性
- **修复前**: 内容被截断为500字符
- **修复后**: 显示完整的告警信息，无长度限制

### 3. 加密支持
- **明文模式**: 直接显示告警内容
- **加密模式**: 显示Unicode乱码符号，需要密码解密

### 4. 字段优先级
```text
位置信息优先级:
position_name > raw_data.positionname > "未知位置"

时间格式处理:
时间戳 → 自动转换为 "YYYY-MM-DD HH:MM:SS"
字符串 → 保持原格式或转换
```

## 🔄 发送触发条件

### 1. 新告警检测
- 检测到新的告警记录
- 按分组键聚合相关告警
- 每组发送一封独立邮件

### 2. 持续告警处理
- 基于持续时间阈值
- 避免重复发送相同告警
- 使用数据库记录发送状态

### 3. 邮件去重机制
```python
# 使用分组键和阈值进行去重
def _sustained_threshold_key(self, alarm):
    root_id = alarm.get('root_group_id', '').strip()
    alarm_id = alarm.get('raw_id', '')
    
    if not root_id:
        return f"独立_{alarm_id}"
    return f"{root_id}_{alarm_id}"
```

## 💡 实际使用场景

### 场景1: 基站故障
```text
根源: 基站主设备故障
├── 衍生: 小区退服 (10个)
├── 衍生: 信号质量下降 (5个)  
└── 衍生: 用户投诉增加 (3个)

结果: 发送1封邮件，包含18个相关告警
```

### 场景2: 多个独立问题
```text
独立告警1: 机房温度异常
独立告警2: 网络延迟超标
独立告警3: 磁盘空间不足

结果: 发送3封独立邮件
```

### 场景3: 混合场景
```text
根源组1: 光缆中断 + 5个衍生告警 → 邮件1
根源组2: 电源故障 + 3个衍生告警 → 邮件2  
独立告警: 温度异常 → 邮件3

结果: 发送3封邮件，内容清晰分类
```

这样的设计确保了：
- ✅ 相关告警聚合在一起，便于分析
- ✅ 不同问题分开处理，避免混淆
- ✅ 邮件内容完整，支持加密
- ✅ 智能去重，避免重复发送
