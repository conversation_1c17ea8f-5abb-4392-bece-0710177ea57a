#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Unicode乱码加密功能
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_unicode_encryption():
    """测试Unicode乱码加密和解密"""
    print("🧪 测试Unicode乱码加密功能")
    print("=" * 60)
    
    # 检查加密库是否可用
    try:
        from cryptography.fernet import Fernet
        from cryptography.hazmat.primitives import hashes
        from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
        import base64
        print("✅ 加密库可用")
    except ImportError:
        print("❌ 加密库不可用，请安装: pip install cryptography")
        return False
    
    # 模拟EmailEncryption类的方法
    def generate_key_from_password(password: str, salt: bytes = None) -> bytes:
        """从密码生成加密密钥"""
        if salt is None:
            salt = b'alarm_monitor_salt_2024'
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        return base64.urlsafe_b64encode(kdf.derive(password.encode()))
    
    def bytes_to_unicode_chaos(data: bytes) -> str:
        """将字节数据转换为Unicode乱码符号"""
        # 使用与主程序相同的符号范围
        symbol_ranges = [
            (0x2600, 0x267F),  # 杂项符号 (缩小范围避免无效字符)
            (0x2700, 0x27BF),  # 装饰符号 ✀✁✂✃✄✅✆✇✈✉
            (0x25A0, 0x25FF),  # 几何图形 ■□▢▣▤▥▦▧▨▩
            (0x2190, 0x21FF),  # 箭头 ←↑→↓↔↕↖↗↘↙
            (0x2660, 0x26FF),  # 杂项符号补充 ♠♡♢♣♤♥♦♧
        ]

        result = ""
        for i, byte_val in enumerate(data):
            range_idx = i % len(symbol_ranges)
            start, end = symbol_ranges[range_idx]
            symbol_range_size = end - start
            symbol_code = start + (byte_val % symbol_range_size)

            try:
                char = chr(symbol_code)
                # 验证字符是否可以正常编码
                char.encode('utf-8')
                result += char
            except (ValueError, UnicodeEncodeError):
                # 如果符号无效，使用安全的备用符号
                safe_code = 0x2600 + (byte_val % 128)
                result += chr(safe_code)

        return result
    
    def unicode_chaos_to_bytes(chaos_text: str) -> bytes:
        """将Unicode乱码符号转换回字节数据"""
        # 必须与编码时使用相同的符号区域
        symbol_ranges = [
            (0x2600, 0x267F),  # 杂项符号 (与编码时一致)
            (0x2700, 0x27BF),  # 装饰符号
            (0x25A0, 0x25FF),  # 几何图形
            (0x2190, 0x21FF),  # 箭头
            (0x2660, 0x26FF),  # 杂项符号补充
        ]

        result = bytearray()
        for i, char in enumerate(chaos_text):
            char_code = ord(char)
            range_idx = i % len(symbol_ranges)
            start, end = symbol_ranges[range_idx]

            if start <= char_code < end:
                byte_val = char_code - start
            else:
                # 备用解码逻辑（处理安全备用符号）
                if 0x2600 <= char_code <= 0x2600 + 127:
                    byte_val = char_code - 0x2600
                else:
                    byte_val = char_code % 256

            result.append(byte_val % 256)

        return bytes(result)
    
    def encrypt_content(content: str, password: str) -> str:
        """加密内容为Unicode乱码"""
        key = generate_key_from_password(password)
        f = Fernet(key)
        encrypted_bytes = f.encrypt(content.encode('utf-8'))
        return bytes_to_unicode_chaos(encrypted_bytes)
    
    def decrypt_content(chaos_content: str, password: str) -> str:
        """解密Unicode乱码内容"""
        key = generate_key_from_password(password)
        f = Fernet(key)
        encrypted_bytes = unicode_chaos_to_bytes(chaos_content)
        decrypted_bytes = f.decrypt(encrypted_bytes)
        return decrypted_bytes.decode('utf-8')
    
    # 测试数据
    test_content = """告警监控系统 - 独立告警通知
发送时间: 2024-08-18 15:30:00
根源分组ID: test_group_123
告警数量: 2 条

============================================================

告警 1:
  告警名称: 小区退服告警
  位置信息: 杭州市西湖区文三路
  网元名称: 基站001
  发生时间: 2024-08-18 10:30:00
  持续时间: 2小时
  关联标记: 🔴根源

告警 2:
  告警名称: 天馈驻波比异常
  位置信息: 杭州市滨江区网商路
  网元名称: 基站002
  发生时间: 2024-08-18 11:00:00
  持续时间: 1小时30分钟
  关联标记: 🟡衍生←小区退服

============================================================

请及时处理相关告警。"""
    
    password = "xjx001515"
    
    print(f"📝 原始内容长度: {len(test_content)} 字符")
    print(f"🔑 加密密码: {password}")
    print()
    
    # 加密测试
    print("🔐 开始加密...")
    try:
        encrypted_chaos = encrypt_content(test_content, password)
        print(f"✅ 加密成功，生成 {len(encrypted_chaos)} 个Unicode符号")
        print()
        
        # 显示加密结果的前100个字符
        print("🎭 加密结果预览（前100个符号）:")
        print(encrypted_chaos[:100])
        print()
        
        # 分析符号类型
        symbol_types = {}
        for char in encrypted_chaos[:50]:  # 分析前50个符号
            code = ord(char)
            if 0x2600 <= code <= 0x26FF:
                symbol_types['杂项符号'] = symbol_types.get('杂项符号', 0) + 1
            elif 0x2700 <= code <= 0x27BF:
                symbol_types['装饰符号'] = symbol_types.get('装饰符号', 0) + 1
            elif 0x1F300 <= code <= 0x1F5FF:
                symbol_types['象形文字'] = symbol_types.get('象形文字', 0) + 1
            elif 0x25A0 <= code <= 0x25FF:
                symbol_types['几何图形'] = symbol_types.get('几何图形', 0) + 1
            elif 0x2190 <= code <= 0x21FF:
                symbol_types['箭头符号'] = symbol_types.get('箭头符号', 0) + 1
            else:
                symbol_types['其他符号'] = symbol_types.get('其他符号', 0) + 1
        
        print("📊 符号类型分析（前50个符号）:")
        for symbol_type, count in symbol_types.items():
            print(f"  {symbol_type}: {count} 个")
        print()
        
    except Exception as e:
        print(f"❌ 加密失败: {e}")
        return False
    
    # 解密测试
    print("🔓 开始解密...")
    try:
        decrypted_content = decrypt_content(encrypted_chaos, password)
        print("✅ 解密成功")
        print()
        
        # 验证解密结果
        if decrypted_content == test_content:
            print("🎉 解密结果完全正确！")
        else:
            print("❌ 解密结果不匹配")
            print(f"原始长度: {len(test_content)}")
            print(f"解密长度: {len(decrypted_content)}")
            return False
            
    except Exception as e:
        print(f"❌ 解密失败: {e}")
        return False
    
    # 测试错误密码
    print("\n🧪 测试错误密码...")
    try:
        wrong_decrypted = decrypt_content(encrypted_chaos, "wrong_password")
        print("❌ 错误密码解密成功（不应该发生）")
        return False
    except Exception:
        print("✅ 错误密码正确被拒绝")
    
    return True

def test_email_format():
    """测试邮件格式"""
    print("\n🧪 测试邮件格式")
    print("=" * 60)
    
    # 模拟加密后的邮件格式
    chaos_content = "☀☁☂☃☄★☆☇☈☉☊☋☌☍☎☏☐☑☒☓☔☕☖☗☘☙☚☛☜☝☞☟☠☡☢☣☤☥☦☧☨☩☪☫☬☭☮☯☰☱☲☳☴☵☶☷☸☹☺☻☼☽☾☿♀♁♂♃♄♅♆♇♈♉♊♋♌♍♎♏♐♑♒♓♔♕♖♗♘♙♚♛♜♝♞♟♠♡♢♣♤♥♦♧♨♩♪♫♬♭♮♯♰♱♲♳♴♵♶♷♸♹♺♻♼♽♾♿⚀⚁⚂⚃⚄⚅⚆⚇⚈⚉⚊⚋⚌⚍⚎⚏⚐⚑⚒⚓⚔⚕⚖⚗⚘⚙⚚⚛⚜⚝⚞⚟⚠⚡⚢⚣⚤⚥⚦⚧⚨⚩⚪⚫⚬⚭⚮⚯⚰⚱⚲⚳⚴⚵⚶⚷⚸⚹⚺⚻⚼⚽⚾⚿⛀⛁⛂⛃⛄⛅⛆⛇⛈⛉⛊⛋⛌⛍⛎⛏⛐⛑⛒⛓⛔⛕⛖⛗⛘⛙⛚⛛⛜⛝⛞⛟⛠⛡⛢⛣⛤⛥⛦⛧⛨⛩⛪⛫⛬⛭⛮⛯⛰⛱⛲⛳⛴⛵⛶⛷⛸⛹⛺⛻⛼⛽⛾⛿"
    
    email_content = f"""🔐 此邮件内容已加密

-----BEGIN ENCRYPTED CONTENT-----
{chaos_content}
-----END ENCRYPTED CONTENT-----

=== 邮件内容已加密（Unicode乱码格式）===
解密方法：
1. 复制加密内容（-----BEGIN ENCRYPTED CONTENT----- 到 -----END ENCRYPTED CONTENT----- 之间的乱码符号）
2. 使用专用解密程序
3. 密码: xjx001515
4. 注意：加密内容显示为各种Unicode符号（☀☁☂★✈🌀■←等），这是正常的加密效果
========================
"""
    
    print("📧 加密邮件格式预览:")
    print("-" * 40)
    print(email_content)
    print("-" * 40)
    
    # 验证格式特点
    checks = [
        ("-----BEGIN ENCRYPTED CONTENT-----" in email_content, "包含开始标记"),
        ("-----END ENCRYPTED CONTENT-----" in email_content, "包含结束标记"),
        ("☀☁☂" in email_content, "包含Unicode乱码符号"),
        ("xjx001515" in email_content, "包含解密密码"),
        ("🔐" in email_content, "包含加密标识"),
    ]
    
    print("\n✅ 格式验证:")
    all_passed = True
    for check, desc in checks:
        status = "✅" if check else "❌"
        print(f"  {status} {desc}")
        if not check:
            all_passed = False
    
    return all_passed

if __name__ == "__main__":
    print("=" * 60)
    print("🎭 Unicode乱码加密测试")
    print("=" * 60)
    
    try:
        # 测试加密解密功能
        test1 = test_unicode_encryption()
        
        # 测试邮件格式
        test2 = test_email_format()
        
        print("\n" + "=" * 60)
        if all([test1, test2]):
            print("🎉 所有测试通过！Unicode乱码加密功能正常")
            print("\n🎭 特点总结:")
            print("• ✅ 加密内容显示为各种Unicode符号（☀☁☂★✈🌀■←等）")
            print("• ✅ 完全无法直接阅读，看起来像真正的乱码")
            print("• ✅ 支持完整的加密解密流程")
            print("• ✅ 错误密码会被正确拒绝")
            print("• ✅ 邮件格式包含解密说明")
            print("• 🔐 比Base64更隐蔽，安全性更高")
        else:
            print("⚠️ 部分测试失败，请检查实现")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
