#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试Unicode乱码加密（Base64映射版本）
"""

def test_base64_unicode_mapping():
    """测试Base64到Unicode符号的映射"""
    print("🧪 测试Base64到Unicode符号映射")
    print("=" * 50)
    
    # Base64字符到Unicode符号的映射表
    char_to_symbol = {
        'A': '☀', 'B': '☁', 'C': '☂', 'D': '☃', 'E': '☄', 'F': '★', 'G': '☆', 'H': '☇',
        'I': '☈', 'J': '☉', 'K': '☊', 'L': '☋', 'M': '☌', 'N': '☍', 'O': '☎', 'P': '☏',
        'Q': '☐', 'R': '☑', 'S': '☒', 'T': '☓', 'U': '☔', 'V': '☕', 'W': '☖', 'X': '☗',
        'Y': '☘', 'Z': '☙', 'a': '☚', 'b': '☛', 'c': '☜', 'd': '☝', 'e': '☞', 'f': '☟',
        'g': '☠', 'h': '☡', 'i': '☢', 'j': '☣', 'k': '☤', 'l': '☥', 'm': '☦', 'n': '☧',
        'o': '☨', 'p': '☩', 'q': '☪', 'r': '☫', 's': '☬', 't': '☭', 'u': '☮', 'v': '☯',
        'w': '☰', 'x': '☱', 'y': '☲', 'z': '☳', '0': '☴', '1': '☵', '2': '☶', '3': '☷',
        '4': '☸', '5': '☹', '6': '☺', '7': '☻', '8': '☼', '9': '☽', '+': '☾', '/': '☿',
        '=': '♀'
    }
    
    # 反向映射表
    symbol_to_char = {v: k for k, v in char_to_symbol.items()}
    
    # 测试数据
    test_data = b"Hello World Test 123"
    print(f"原始数据: {test_data}")
    
    # 转换为Base64
    import base64
    b64_data = base64.b64encode(test_data).decode('ascii')
    print(f"Base64: {b64_data}")
    
    # 转换为Unicode符号
    unicode_symbols = ""
    for char in b64_data:
        unicode_symbols += char_to_symbol.get(char, '♁')
    
    print(f"Unicode符号: {unicode_symbols}")
    print(f"符号长度: {len(unicode_symbols)}")
    
    # 从Unicode符号恢复Base64
    recovered_b64 = ""
    for symbol in unicode_symbols:
        recovered_b64 += symbol_to_char.get(symbol, 'A')
    
    print(f"恢复Base64: {recovered_b64}")
    
    # 解码Base64
    try:
        recovered_data = base64.b64decode(recovered_b64.encode('ascii'))
        print(f"恢复数据: {recovered_data}")
        
        if recovered_data == test_data:
            print("✅ Base64映射转换成功！")
            return True
        else:
            print("❌ Base64映射转换失败")
            return False
    except Exception as e:
        print(f"❌ Base64解码失败: {e}")
        return False

def test_complete_unicode_encryption():
    """测试完整的Unicode乱码加密流程"""
    print("\n🧪 测试完整Unicode乱码加密流程")
    print("=" * 50)
    
    try:
        from cryptography.fernet import Fernet
        from cryptography.hazmat.primitives import hashes
        from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
        import base64
    except ImportError:
        print("❌ 加密库不可用")
        return False
    
    # 测试内容
    content = """告警监控系统 - 独立告警通知
发送时间: 2024-08-18 15:30:00
根源分组ID: test_group_123
告警数量: 1 条

告警 1:
  告警名称: 小区退服告警
  位置信息: 杭州市西湖区文三路
  网元名称: 基站001
  发生时间: 2024-08-18 10:30:00
  持续时间: 2小时
  关联标记: 🔴根源

请及时处理相关告警。"""
    
    password = "xjx001515"
    
    print(f"原始内容长度: {len(content)} 字符")
    print(f"密码: {password}")
    
    # 生成密钥
    salt = b'alarm_monitor_salt_2024'
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )
    key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
    
    # 加密
    f = Fernet(key)
    encrypted_bytes = f.encrypt(content.encode('utf-8'))
    print(f"加密字节长度: {len(encrypted_bytes)}")
    
    # 转换为Unicode乱码（Base64映射版本）
    def encrypt_to_unicode_chaos(data: bytes) -> str:
        # 映射表
        char_to_symbol = {
            'A': '☀', 'B': '☁', 'C': '☂', 'D': '☃', 'E': '☄', 'F': '★', 'G': '☆', 'H': '☇',
            'I': '☈', 'J': '☉', 'K': '☊', 'L': '☋', 'M': '☌', 'N': '☍', 'O': '☎', 'P': '☏',
            'Q': '☐', 'R': '☑', 'S': '☒', 'T': '☓', 'U': '☔', 'V': '☕', 'W': '☖', 'X': '☗',
            'Y': '☘', 'Z': '☙', 'a': '☚', 'b': '☛', 'c': '☜', 'd': '☝', 'e': '☞', 'f': '☟',
            'g': '☠', 'h': '☡', 'i': '☢', 'j': '☣', 'k': '☤', 'l': '☥', 'm': '☦', 'n': '☧',
            'o': '☨', 'p': '☩', 'q': '☪', 'r': '☫', 's': '☬', 't': '☭', 'u': '☮', 'v': '☯',
            'w': '☰', 'x': '☱', 'y': '☲', 'z': '☳', '0': '☴', '1': '☵', '2': '☶', '3': '☷',
            '4': '☸', '5': '☹', '6': '☺', '7': '☻', '8': '☼', '9': '☽', '+': '☾', '/': '☿',
            '=': '♀'
        }
        
        b64_data = base64.b64encode(data).decode('ascii')
        result = ""
        for char in b64_data:
            result += char_to_symbol.get(char, '♁')
        return result
    
    def decrypt_from_unicode_chaos(chaos_text: str) -> bytes:
        # 反向映射表
        symbol_to_char = {
            '☀': 'A', '☁': 'B', '☂': 'C', '☃': 'D', '☄': 'E', '★': 'F', '☆': 'G', '☇': 'H',
            '☈': 'I', '☉': 'J', '☊': 'K', '☋': 'L', '☌': 'M', '☍': 'N', '☎': 'O', '☏': 'P',
            '☐': 'Q', '☑': 'R', '☒': 'S', '☓': 'T', '☔': 'U', '☕': 'V', '☖': 'W', '☗': 'X',
            '☘': 'Y', '☙': 'Z', '☚': 'a', '☛': 'b', '☜': 'c', '☝': 'd', '☞': 'e', '☟': 'f',
            '☠': 'g', '☡': 'h', '☢': 'i', '☣': 'j', '☤': 'k', '☥': 'l', '☦': 'm', '☧': 'n',
            '☨': 'o', '☩': 'p', '☪': 'q', '☫': 'r', '☬': 's', '☭': 't', '☮': 'u', '☯': 'v',
            '☰': 'w', '☱': 'x', '☲': 'y', '☳': 'z', '☴': '0', '☵': '1', '☶': '2', '☷': '3',
            '☸': '4', '☹': '5', '☺': '6', '☻': '7', '☼': '8', '☽': '9', '☾': '+', '☿': '/',
            '♀': '='
        }
        
        b64_data = ""
        for symbol in chaos_text:
            b64_data += symbol_to_char.get(symbol, 'A')
        
        try:
            return base64.b64decode(b64_data.encode('ascii'))
        except Exception:
            return b''
    
    # 加密并转换为Unicode乱码
    chaos_content = encrypt_to_unicode_chaos(encrypted_bytes)
    print(f"Unicode乱码长度: {len(chaos_content)}")
    print(f"Unicode乱码前30个: {chaos_content[:30]}")
    
    # 从Unicode乱码恢复并解密
    recovered_bytes = decrypt_from_unicode_chaos(chaos_content)
    
    if recovered_bytes == encrypted_bytes:
        print("✅ Unicode转换成功")
    else:
        print("❌ Unicode转换失败")
        return False
    
    # 解密
    try:
        decrypted_bytes = f.decrypt(recovered_bytes)
        decrypted_content = decrypted_bytes.decode('utf-8')
        
        if decrypted_content == content:
            print("✅ 完整加密解密成功！")
            print(f"解密内容前100字符: {decrypted_content[:100]}...")
            return True
        else:
            print("❌ 解密内容不匹配")
            return False
    except Exception as e:
        print(f"❌ 解密失败: {e}")
        return False

def show_unicode_chaos_example():
    """展示Unicode乱码效果"""
    print("\n🎭 Unicode乱码效果展示")
    print("=" * 50)
    
    # 示例邮件内容
    sample_email = """🔐 此邮件内容已加密

-----BEGIN ENCRYPTED CONTENT-----
☀☁☂☃☄★☆☇☈☉☊☋☌☍☎☏☐☑☒☓☔☕☖☗☘☙☚☛☜☝☞☟☠☡☢☣☤☥☦☧☨☩☪☫☬☭☮☯☰☱☲☳☴☵☶☷☸☹☺☻☼☽☾☿♀☀☁☂☃☄★☆☇☈☉☊☋☌☍☎☏☐☑☒☓☔☕☖☗☘☙☚☛☜☝☞☟☠☡☢☣☤☥☦☧☨☩☪☫☬☭☮☯☰☱☲☳☴☵☶☷☸☹☺☻☼☽☾☿♀☀☁☂☃☄★☆☇☈☉☊☋☌☍☎☏☐☑☒☓☔☕☖☗☘☙☚☛☜☝☞☟☠☡☢☣☤☥☦☧☨☩☪☫☬☭☮☯☰☱☲☳☴☵☶☷☸☹☺☻☼☽☾☿♀
-----END ENCRYPTED CONTENT-----

密码: xjx001515
注意：加密内容显示为各种Unicode符号，这是正常的加密效果"""
    
    print("📧 加密邮件效果:")
    print("-" * 40)
    print(sample_email)
    print("-" * 40)
    
    print("\n🎯 特点:")
    print("• 完全无法直接阅读")
    print("• 看起来像各种天气、符号等Unicode字符")
    print("• 比Base64更隐蔽，不容易被识别为加密内容")
    print("• 支持完整的加密解密流程")
    print("• 基于Base64映射，100%可逆")

if __name__ == "__main__":
    print("🎭 Unicode乱码加密最终测试")
    print("=" * 60)
    
    # 测试Base64映射
    test1 = test_base64_unicode_mapping()
    
    # 测试完整加密流程
    test2 = test_complete_unicode_encryption()
    
    # 展示效果
    show_unicode_chaos_example()
    
    print("\n" + "=" * 60)
    if test1 and test2:
        print("🎉 Unicode乱码加密完美工作！")
        print("\n🔐 现在你的邮件将显示为:")
        print("☀☁☂☃☄★☆☇☈☉☊☋☌☍☎☏☐☑☒☓☔☕☖☗☘☙...")
        print("完全看不出是加密内容，只像是一堆奇怪的符号！")
    else:
        print("❌ 仍有问题需要修复")
    print("=" * 60)
