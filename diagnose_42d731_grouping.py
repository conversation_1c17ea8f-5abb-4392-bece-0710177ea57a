#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断42d731分组问题
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def diagnose_grouping_issue():
    """诊断42d731为什么被分组为独立告警"""
    print("🔍 诊断42d731分组问题")
    print("=" * 60)
    
    print("📋 分组逻辑分析:")
    print("根据代码，告警被分组为独立的原因是：")
    print("1. root_group_id 为空字符串或None")
    print("2. 分组键生成逻辑：")
    print("   - 如果 root_group_id 为空 → 生成 '独立_{alarm_key前6位}'")
    print("   - 如果 root_group_id 不为空 → 生成 '{root_group_id前6位}'")
    
    print("\n🔍 root_group_id 生成逻辑:")
    print("1. 根源告警 (relationflag=1): root_group_id = 自身的alarm_key")
    print("2. 衍生告警 (relationflag=2): root_group_id = correlation_info.get('root_alarmkey', '')")
    print("3. 次根源告警 (relationflag=3): root_group_id = correlation_info.get('root_alarmkey', '')")
    print("4. 独立告警 (relationflag=0): root_group_id = ''")
    
    print("\n❓ 42d731被分组为独立的可能原因:")
    print("1. ❌ relationflag = 0 (真正的独立告警)")
    print("2. ❌ relationflag = 2或3，但追根失败 (root_alarmkey为空)")
    print("3. ❌ relationflag = 1，但alarm_key为空")
    print("4. ❌ 数据处理过程中root_group_id被意外清空")
    
    print("\n🔧 诊断步骤:")
    print("需要检查以下信息来确定根本原因：")
    
    # 模拟诊断过程
    test_cases = [
        {
            'alarm_key': '42d731abcdef123456789',
            'relationflag': 1,
            'root_group_id': '42d731abcdef123456789',
            'expected_group': '42d731',
            'actual_group': '独立_42d731',
            'issue': 'root_group_id被意外清空'
        },
        {
            'alarm_key': '42d731abcdef123456789',
            'relationflag': 2,
            'root_group_id': '',
            'correlation_info': {'root_alarmkey': ''},
            'expected_group': '42d731',
            'actual_group': '独立_42d731',
            'issue': '追根失败，root_alarmkey为空'
        },
        {
            'alarm_key': '42d731abcdef123456789',
            'relationflag': 3,
            'root_group_id': '',
            'correlation_info': {'root_alarmkey': ''},
            'expected_group': '42d731',
            'actual_group': '独立_42d731',
            'issue': '追根失败，root_alarmkey为空'
        }
    ]
    
    print("\n📊 可能的问题场景:")
    for i, case in enumerate(test_cases, 1):
        print(f"\n场景 {i}: {case['issue']}")
        print(f"  alarm_key: {case['alarm_key']}")
        print(f"  relationflag: {case['relationflag']}")
        print(f"  root_group_id: '{case['root_group_id']}'")
        if 'correlation_info' in case:
            print(f"  correlation_info: {case['correlation_info']}")
        print(f"  期望分组: {case['expected_group']}")
        print(f"  实际分组: {case['actual_group']}")
        print(f"  问题: {case['issue']}")
    
    return True

def check_correlation_analysis():
    """检查关联分析可能的问题"""
    print("\n🔍 关联分析问题检查")
    print("=" * 60)
    
    print("📋 关联分析失败的可能原因:")
    print("1. ❌ 父级信息 (parentinfo) 为空或格式错误")
    print("2. ❌ 父级告警在数据库中不存在")
    print("3. ❌ 父级告警的alarm_key格式不正确")
    print("4. ❌ 关联分析算法超时或异常")
    print("5. ❌ 数据库查询失败")
    
    print("\n🔧 关联分析流程:")
    print("1. 解析 parentinfo 字段（五段式格式）")
    print("2. 提取父级告警的 alarm_key")
    print("3. 在 key_to_alarm 映射中查找父级告警")
    print("4. 递归追溯到根源告警")
    print("5. 返回根源告警的 alarm_key 作为 root_alarmkey")
    
    print("\n❓ 42d731相关告警的关联分析:")
    print("需要检查：")
    print("- 42d731开头的告警有哪些？")
    print("- 它们的relationflag分别是什么？")
    print("- 它们的parentinfo内容是什么？")
    print("- 关联分析的结果是什么？")
    
    return True

def suggest_debugging_steps():
    """建议调试步骤"""
    print("\n🔧 建议的调试步骤")
    print("=" * 60)
    
    print("📋 立即检查项目:")
    print("1. 🔍 查看程序日志中的分组验证信息")
    print("   - 搜索 '分组验证：共' 关键词")
    print("   - 查看 '独立_42d731' 分组的详细信息")
    
    print("\n2. 🔍 检查42d731相关告警的详细信息")
    print("   - alarm_key 完整值")
    print("   - relationflag 值")
    print("   - root_group_id 值")
    print("   - parentinfo 内容")
    
    print("\n3. 🔍 检查关联分析结果")
    print("   - correlation_results 中42d731的条目")
    print("   - root_alarmkey 是否为空")
    print("   - 追根失败的错误信息")
    
    print("\n📋 代码调试建议:")
    print("1. 在分组验证逻辑中添加更详细的日志")
    print("2. 在关联分析中添加调试输出")
    print("3. 检查数据库中42d731相关告警的原始数据")
    
    debug_code = '''
# 在 _validate_grouping_logic 方法中添加：
if group_key == '独立_42d731':
    print(f"🔍 调试独立_42d731分组:")
    for alarm in alarms:
        print(f"  告警: {alarm.get('code_name', '未知')}")
        print(f"  alarm_key: {alarm.get('alarm_key', '')}")
        print(f"  relationflag: {alarm.get('relationflag', 0)}")
        print(f"  root_group_id: '{alarm.get('root_group_id', '')}'")
        print(f"  parentinfo: {alarm.get('raw_data', {}).get('parentinfo', '')}")
        print()
'''
    
    print("\n💻 调试代码示例:")
    print(debug_code)
    
    return True

def analyze_grouping_algorithm():
    """分析分组算法的逻辑"""
    print("\n🔍 分组算法逻辑分析")
    print("=" * 60)
    
    print("📋 分组键生成算法:")
    print("```python")
    print("def _group_key_short(self, a):")
    print("    root_id = (a.get('root_group_id') or '').strip()")
    print("    if not root_id:")
    print("        # 独立告警：安全获取alarm_key")
    print("        ak = self._safe_get_alarm_key(a)")
    print("        return f'独立_{self._short_id(ak)}'")
    print("    return self._short_id(root_id)")
    print("```")
    
    print("\n🎯 关键判断点:")
    print("1. root_group_id 是否为空？")
    print("   - 空字符串 '' → 独立告警")
    print("   - None → 独立告警")
    print("   - 只有空格 '   ' → 独立告警（strip后为空）")
    print("   - 有值 → 关联告警")
    
    print("\n2. _short_id 截取逻辑:")
    print("   - 取前6位字符")
    print("   - 42d731abcdef → 42d731")
    
    print("\n❓ 42d731的问题分析:")
    print("如果42d731被分组为 '独立_42d731'，说明：")
    print("1. ✅ alarm_key 确实是 42d731开头")
    print("2. ❌ root_group_id 为空")
    print("3. ❓ 为什么 root_group_id 为空？")
    
    print("\n🔍 需要检查的数据流:")
    print("数据库 → 关联分析 → root_group_id生成 → 分组键生成")
    print("     ↓         ↓           ↓            ↓")
    print("  parentinfo  correlation  root_group_id  group_key")
    print("             results")
    
    return True

if __name__ == "__main__":
    print("🔍 42d731分组问题诊断")
    print("=" * 80)
    
    try:
        # 诊断分组问题
        test1 = diagnose_grouping_issue()
        
        # 检查关联分析
        test2 = check_correlation_analysis()
        
        # 建议调试步骤
        test3 = suggest_debugging_steps()
        
        # 分析分组算法
        test4 = analyze_grouping_algorithm()
        
        print("\n" + "=" * 80)
        if all([test1, test2, test3, test4]):
            print("🎯 诊断总结:")
            print("\n42d731被分组为独立告警的根本原因是：")
            print("❌ root_group_id 字段为空")
            print("\n可能的具体原因：")
            print("1. 关联分析失败 (relationflag=2或3时)")
            print("2. alarm_key为空 (relationflag=1时)")
            print("3. 数据处理错误")
            print("\n🔧 解决方案：")
            print("1. 检查程序日志中的分组验证信息")
            print("2. 查看42d731相关告警的详细数据")
            print("3. 检查关联分析的结果和错误信息")
            print("4. 添加调试代码输出更多信息")
            print("\n💡 如果确实有同组告警，它们应该有相同的root_group_id")
            print("   检查这些告警的root_group_id是否一致")
        else:
            print("⚠️ 诊断过程中出现问题")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        import traceback
        traceback.print_exc()
