#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试：验证拆分邮件功能是否正确工作
"""

import sys
import os
from datetime import datetime

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_split_email_mechanism():
    """测试拆分邮件的机制说明"""
    print("🧪 拆分邮件机制测试")
    print("=" * 50)
    
    # 模拟5条告警的分组情况
    mock_alarms = [
        {'alarm_key': 'alarm_001', 'code_name': '小区退服告警', 'root_group': 'group_A'},
        {'alarm_key': 'alarm_002', 'code_name': '天馈驻波比异常', 'root_group': 'group_A'},
        {'alarm_key': 'alarm_003', 'code_name': '光功率异常', 'root_group': 'group_B'},
        {'alarm_key': 'alarm_004', 'code_name': '链路故障', 'root_group': 'group_B'},
        {'alarm_key': 'alarm_005', 'code_name': 'CPU过载', 'root_group': 'group_B'},
    ]
    
    # 按根源组分组
    groups = {}
    for alarm in mock_alarms:
        group_key = alarm['root_group']
        if group_key not in groups:
            groups[group_key] = []
        groups[group_key].append(alarm)
    
    print(f"📊 总告警数: {len(mock_alarms)} 条")
    print(f"📊 分组数量: {len(groups)} 组")
    print()
    
    print("📧 拆分发送机制说明:")
    print("=" * 30)
    print("❌ 不是按单个告警发送（那样会发5封邮件）")
    print("✅ 是按告警组发送（发送2封邮件）")
    print()
    
    for i, (group_key, group_alarms) in enumerate(groups.items(), 1):
        print(f"📧 邮件 {i} - 组 {group_key}:")
        for alarm in group_alarms:
            print(f"   • {alarm['code_name']}")
        print(f"   共 {len(group_alarms)} 条告警")
        print()
    
    print("🎯 关键点:")
    print("• 每个根源组 = 一封邮件")
    print("• 同组的多个告警会在同一封邮件中")
    print("• 发送间隔2秒，避免邮件服务器限制")
    print("• 每封邮件独立发送，失败不影响其他邮件")
    
    return True

def test_email_content_generation():
    """测试邮件内容生成"""
    print("\n🧪 邮件内容生成测试")
    print("=" * 50)
    
    # 模拟一个告警组
    group_key = "abc12345-def6-7890-abcd-ef1234567890"
    group_alarms = [
        {
            'alarm_key': 'alarm_001',
            'code_name': '小区退服告警',
            'me_name': '基站001',
            'ne_ip': '***********',
            'perceived_severity_name': '严重',
            'alarm_raised_time': '2024-01-15 10:30:00',
            'effective_duration_minutes': 120
        },
        {
            'alarm_key': 'alarm_002',
            'code_name': '天馈驻波比异常',
            'me_name': '基站001',
            'ne_ip': '***********',
            'perceived_severity_name': '重要',
            'alarm_raised_time': '2024-01-15 10:35:00',
            'effective_duration_minutes': 115
        }
    ]
    
    # 生成邮件内容
    content = generate_test_group_email_content(group_key, group_alarms)
    
    print("📧 生成的邮件内容预览:")
    print("-" * 40)
    print(content[:500] + "..." if len(content) > 500 else content)
    print("-" * 40)
    
    # 验证关键信息
    checks = [
        ("根源分组ID" in content, "包含根源分组ID"),
        ("告警数量: 2 条" in content, "正确显示告警数量"),
        ("小区退服告警" in content, "包含第一个告警"),
        ("天馈驻波比异常" in content, "包含第二个告警"),
        ("2小时" in content, "包含持续时间"),
    ]
    
    print("\n✅ 内容验证:")
    for check, desc in checks:
        status = "✅" if check else "❌"
        print(f"  {status} {desc}")
    
    return all(check for check, _ in checks)

def generate_test_group_email_content(group_key, group_alarms):
    """生成测试邮件内容"""
    import uuid
    
    content_lines = []
    content_lines.append(f"告警监控系统 - 独立告警通知")
    content_lines.append(f"发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    content_lines.append(f"根源分组ID: {group_key}")
    content_lines.append(f"告警数量: {len(group_alarms)} 条")
    content_lines.append("")
    content_lines.append("=" * 60)
    content_lines.append("")
    
    # 添加告警详情
    for i, alarm in enumerate(group_alarms, 1):
        content_lines.append(f"告警 {i}:")
        content_lines.append(f"  告警名称: {alarm.get('code_name', '未知')}")
        content_lines.append(f"  网元名称: {alarm.get('me_name', '未知')}")
        content_lines.append(f"  IP地址: {alarm.get('ne_ip', '未知')}")
        content_lines.append(f"  告警级别: {alarm.get('perceived_severity_name', '未知')}")
        content_lines.append(f"  发生时间: {alarm.get('alarm_raised_time', '未知')}")
        
        # 添加持续时间信息
        duration_minutes = alarm.get('effective_duration_minutes', 0)
        if duration_minutes > 0:
            hours = duration_minutes // 60
            mins = duration_minutes % 60
            if hours > 0:
                duration_text = f"{hours}小时{mins}分钟" if mins > 0 else f"{hours}小时"
            else:
                duration_text = f"{mins}分钟"
            content_lines.append(f"  持续时间: {duration_text}")
        
        content_lines.append("")
    
    content_lines.append("=" * 60)
    content_lines.append("")
    content_lines.append("请及时处理相关告警。")
    content_lines.append("")
    content_lines.append(f"<!-- UUID: {str(uuid.uuid4())} -->")
    
    return "\n".join(content_lines)

def test_code_cleanup_verification():
    """验证代码清理是否完成"""
    print("\n🧪 代码清理验证")
    print("=" * 50)
    
    # 检查主程序文件
    main_file = "alarm_monitor_pyside6.py"
    if not os.path.exists(main_file):
        print("❌ 主程序文件不存在")
        return False
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否还有合并发送的痕迹
    cleanup_checks = [
        ("maybe_send_consolidated_email" not in content, "删除了旧的整合邮件方法调用"),
        ("send_merged_email" not in content, "删除了合并发送方法"),
        ("send_individual_emails" in content, "保留了拆分发送方法"),
        ("compose_single_group_email" in content, "保留了单组邮件生成方法"),
    ]
    
    print("🔍 代码清理检查:")
    all_passed = True
    for check, desc in cleanup_checks:
        status = "✅" if check else "❌"
        print(f"  {status} {desc}")
        if not check:
            all_passed = False
    
    return all_passed

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 拆分邮件功能最终测试")
    print("=" * 60)
    
    try:
        # 测试拆分机制
        test1 = test_split_email_mechanism()
        
        # 测试邮件内容生成
        test2 = test_email_content_generation()
        
        # 验证代码清理
        test3 = test_code_cleanup_verification()
        
        print("\n" + "=" * 60)
        if all([test1, test2, test3]):
            print("🎉 所有测试通过！拆分邮件功能已完成")
            print("\n📧 现在系统将:")
            print("• 按告警组发送独立邮件（不是按单个告警）")
            print("• 每组告警一封邮件，组内多个告警在同一封邮件中")
            print("• 发送间隔2秒，避免邮件服务器限制")
            print("• 彻底删除了合并发送逻辑")
        else:
            print("⚠️ 部分测试失败，请检查实现")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
