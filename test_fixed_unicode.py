#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的Unicode乱码加密
"""

def test_fixed_unicode_conversion():
    """测试修复后的Unicode转换"""
    print("🧪 测试修复后的Unicode转换")
    print("=" * 50)
    
    # 测试数据
    test_bytes = b"Hello World Test 123"
    print(f"原始字节: {test_bytes}")
    print(f"字节长度: {len(test_bytes)}")
    
    # 修复后的编码逻辑
    def bytes_to_unicode_chaos_fixed(data: bytes) -> str:
        result = ""
        for i, byte_val in enumerate(data):
            if i % 5 == 0:  # 杂项符号
                symbol_code = 0x2600 + (byte_val % 128)
            elif i % 5 == 1:  # 装饰符号
                symbol_code = 0x2700 + (byte_val % 128)
            elif i % 5 == 2:  # 几何图形
                symbol_code = 0x25A0 + (byte_val % 96)
            elif i % 5 == 3:  # 箭头
                symbol_code = 0x2190 + (byte_val % 112)
            else:  # 杂项符号补充
                symbol_code = 0x2660 + (byte_val % 128)
            
            try:
                char = chr(symbol_code)
                char.encode('utf-8')
                result += char
            except (ValueError, UnicodeEncodeError):
                safe_code = 0x2600 + (byte_val % 64)
                result += chr(safe_code)
        
        return result
    
    # 修复后的解码逻辑
    def unicode_chaos_to_bytes_fixed(chaos_text: str) -> bytes:
        result = bytearray()
        for i, char in enumerate(chaos_text):
            char_code = ord(char)
            
            if i % 5 == 0:  # 杂项符号
                if 0x2600 <= char_code <= 0x267F:
                    byte_val = char_code - 0x2600
                else:
                    byte_val = char_code - 0x2600
            elif i % 5 == 1:  # 装饰符号
                if 0x2700 <= char_code <= 0x277F:
                    byte_val = char_code - 0x2700
                else:
                    byte_val = char_code % 256
            elif i % 5 == 2:  # 几何图形
                if 0x25A0 <= char_code <= 0x25FF:
                    byte_val = char_code - 0x25A0
                else:
                    byte_val = char_code % 256
            elif i % 5 == 3:  # 箭头
                if 0x2190 <= char_code <= 0x21FF:
                    byte_val = char_code - 0x2190
                else:
                    byte_val = char_code % 256
            else:  # 杂项符号补充
                if 0x2660 <= char_code <= 0x26DF:
                    byte_val = char_code - 0x2660
                else:
                    byte_val = char_code % 256
            
            result.append(byte_val % 256)
        
        return bytes(result)
    
    # 编码
    print("\n🔐 编码...")
    chaos_result = bytes_to_unicode_chaos_fixed(test_bytes)
    print(f"乱码结果: {chaos_result}")
    print(f"乱码长度: {len(chaos_result)}")
    
    # 解码
    print("\n🔓 解码...")
    decoded_result = unicode_chaos_to_bytes_fixed(chaos_result)
    print(f"解码结果: {decoded_result}")
    print(f"解码长度: {len(decoded_result)}")
    
    # 验证
    if decoded_result == test_bytes:
        print("✅ 修复后的转换成功！")
        return True
    else:
        print("❌ 修复后的转换仍然失败")
        print(f"原始: {list(test_bytes)}")
        print(f"解码: {list(decoded_result)}")
        return False

def test_complete_encryption():
    """测试完整的加密解密流程"""
    print("\n🧪 测试完整加密解密流程")
    print("=" * 50)
    
    try:
        from cryptography.fernet import Fernet
        from cryptography.hazmat.primitives import hashes
        from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
        import base64
    except ImportError:
        print("❌ 加密库不可用")
        return False
    
    # 测试内容
    content = """告警监控系统 - 独立告警通知
发送时间: 2024-08-18 15:30:00
告警数量: 1 条

告警 1:
  告警名称: 小区退服告警
  位置信息: 杭州市西湖区
  网元名称: 基站001
  发生时间: 2024-08-18 10:30:00
  持续时间: 2小时
  关联标记: 🔴根源"""
    
    password = "xjx001515"
    
    print(f"原始内容长度: {len(content)} 字符")
    print(f"密码: {password}")
    
    # 生成密钥
    salt = b'alarm_monitor_salt_2024'
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )
    key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
    
    # 加密
    f = Fernet(key)
    encrypted_bytes = f.encrypt(content.encode('utf-8'))
    print(f"加密字节长度: {len(encrypted_bytes)}")
    
    # 转换为Unicode乱码（使用修复后的逻辑）
    def bytes_to_unicode_chaos_fixed(data: bytes) -> str:
        result = ""
        for i, byte_val in enumerate(data):
            if i % 5 == 0:
                symbol_code = 0x2600 + (byte_val % 128)
            elif i % 5 == 1:
                symbol_code = 0x2700 + (byte_val % 128)
            elif i % 5 == 2:
                symbol_code = 0x25A0 + (byte_val % 96)
            elif i % 5 == 3:
                symbol_code = 0x2190 + (byte_val % 112)
            else:
                symbol_code = 0x2660 + (byte_val % 128)
            
            try:
                char = chr(symbol_code)
                char.encode('utf-8')
                result += char
            except (ValueError, UnicodeEncodeError):
                safe_code = 0x2600 + (byte_val % 64)
                result += chr(safe_code)
        
        return result
    
    def unicode_chaos_to_bytes_fixed(chaos_text: str) -> bytes:
        result = bytearray()
        for i, char in enumerate(chaos_text):
            char_code = ord(char)
            
            if i % 5 == 0:
                if 0x2600 <= char_code <= 0x267F:
                    byte_val = char_code - 0x2600
                else:
                    byte_val = char_code - 0x2600
            elif i % 5 == 1:
                if 0x2700 <= char_code <= 0x277F:
                    byte_val = char_code - 0x2700
                else:
                    byte_val = char_code % 256
            elif i % 5 == 2:
                if 0x25A0 <= char_code <= 0x25FF:
                    byte_val = char_code - 0x25A0
                else:
                    byte_val = char_code % 256
            elif i % 5 == 3:
                if 0x2190 <= char_code <= 0x21FF:
                    byte_val = char_code - 0x2190
                else:
                    byte_val = char_code % 256
            else:
                if 0x2660 <= char_code <= 0x26DF:
                    byte_val = char_code - 0x2660
                else:
                    byte_val = char_code % 256
            
            result.append(byte_val % 256)
        
        return bytes(result)
    
    chaos_content = bytes_to_unicode_chaos_fixed(encrypted_bytes)
    print(f"Unicode乱码长度: {len(chaos_content)}")
    print(f"Unicode乱码前20个: {chaos_content[:20]}")
    
    # 从Unicode乱码恢复
    recovered_bytes = unicode_chaos_to_bytes_fixed(chaos_content)
    
    # 验证字节恢复
    if recovered_bytes == encrypted_bytes:
        print("✅ Unicode转换成功")
    else:
        print("❌ Unicode转换失败")
        return False
    
    # 解密
    try:
        decrypted_bytes = f.decrypt(recovered_bytes)
        decrypted_content = decrypted_bytes.decode('utf-8')
        
        if decrypted_content == content:
            print("✅ 完整加密解密成功！")
            print(f"解密内容前100字符: {decrypted_content[:100]}...")
            return True
        else:
            print("❌ 解密内容不匹配")
            return False
    except Exception as e:
        print(f"❌ 解密失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 测试修复后的Unicode乱码加密")
    print("=" * 60)
    
    # 测试基础转换
    test1 = test_fixed_unicode_conversion()
    
    # 测试完整流程
    test2 = test_complete_encryption()
    
    print("\n" + "=" * 60)
    if test1 and test2:
        print("🎉 修复成功！Unicode乱码加密正常工作")
    else:
        print("❌ 仍有问题需要修复")
    print("=" * 60)
