#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件连发测试GUI工具 - 修复版本
专门用于测试联通企业邮箱的稳定性
"""

import sys
import smtplib
import imaplib
import email
import time
import threading
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import re

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                               QWidget, QLabel, QLineEdit, QPushButton, QTextEdit, 
                               QComboBox, QCheckBox, QProgressBar, QGroupBox, 
                               QMessageBox, QTabWidget, QSpinBox, QFormLayout)
from PySide6.QtCore import Qt, QThread, Signal
from PySide6.QtGui import QFont, QTextCursor

class EmailTestWorker(QThread):
    """邮件测试工作线程"""
    progress_updated = Signal(int, str)  # 进度, 消息
    test_completed = Signal(dict)  # 测试结果
    
    def __init__(self, config, count, check_received=False):
        super().__init__()
        self.config = config
        self.count = count
        self.check_received = check_received
        self.test_id = datetime.now().strftime('%Y%m%d_%H%M%S')
        
    def run(self):
        """执行邮件测试"""
        try:
            result = self.send_batch_emails()
            if self.check_received and result['success_count'] > 0:
                received_result = self.check_received_emails(result['start_time'], result['success_count'])
                result.update(received_result)
            self.test_completed.emit(result)
        except Exception as e:
            self.progress_updated.emit(100, f"❌ 测试失败: {e}")
            
    def send_batch_emails(self):
        """发送批量邮件"""
        success_count = 0
        failed_count = 0
        send_times = []
        start_time = datetime.now()
        
        self.progress_updated.emit(0, f"📬 开始连发测试: {self.count}封邮件")
        self.progress_updated.emit(0, f"🆔 测试ID: {self.test_id}")
        
        for i in range(self.count):
            try:
                # 创建测试邮件
                msg = MIMEMultipart('alternative')
                current_time = datetime.now()
                msg['Subject'] = f"[BatchTest {self.test_id}_{i+1:02d}/{self.count:02d}] Email Stability Test - {current_time.strftime('%H:%M:%S')}"
                msg['From'] = self.config['from_addr']
                msg['To'] = self.config['to_addr']
                
                test_content = f"""
Email Batch Stability Test

Test ID: {self.test_id}
Email Number: {i+1:02d}/{self.count:02d}
Send Time: {current_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}
Email Type: China Unicom Enterprise Email
From: {self.config['from_addr']}
To: {self.config['to_addr']}

Test Purpose:
1. Detect email sending success rate
2. Test for email loss
3. Observe email arrival time differences
4. Verify enterprise email optimization

Alarm Monitoring System - Batch Test #{i+1:02d}
Test Time: {current_time.isoformat()}
Test Batch: {self.test_id}
"""
                msg.attach(MIMEText(test_content, 'plain', 'utf-8'))
                
                # 发送邮件
                send_start = time.time()
                
                if self.config['use_ssl']:
                    server = smtplib.SMTP_SSL(self.config['smtp_host'], self.config['smtp_port'], timeout=45)
                else:
                    server = smtplib.SMTP(self.config['smtp_host'], self.config['smtp_port'], timeout=45)
                
                server.login(self.config['username'], self.config['password'])
                result = server.sendmail(self.config['from_addr'], [self.config['to_addr']], msg.as_string())
                server.quit()
                
                send_duration = time.time() - send_start
                send_times.append(send_duration)
                
                if result:
                    self.progress_updated.emit(int((i+1)/self.count*50), f"📧 [{i+1:02d}/{self.count:02d}] 发送完成但有警告 ({send_duration:.1f}s)")
                    success_count += 1
                else:
                    self.progress_updated.emit(int((i+1)/self.count*50), f"📧 [{i+1:02d}/{self.count:02d}] 发送成功 ({send_duration:.1f}s)")
                    success_count += 1
                
                # 企业邮箱增加间隔
                if i < self.count - 1:
                    time.sleep(3.0)
                    
            except Exception as e:
                self.progress_updated.emit(int((i+1)/self.count*50), f"❌ [{i+1:02d}/{self.count:02d}] 发送失败: {e}")
                failed_count += 1
        
        total_time = (datetime.now() - start_time).total_seconds()
        avg_send_time = sum(send_times) / len(send_times) if send_times else 0
        
        return {
            'test_id': self.test_id,
            'start_time': start_time,
            'count': self.count,
            'success_count': success_count,
            'failed_count': failed_count,
            'total_time': total_time,
            'avg_send_time': avg_send_time,
            'send_success_rate': (success_count/self.count*100) if self.count > 0 else 0
        }
    
    def check_received_emails(self, test_start_time, expected_count):
        """检查接收端邮件"""
        self.progress_updated.emit(60, "🔍 开始检查接收端邮件...")
        
        if not self.config.get('imap_password'):
            self.progress_updated.emit(70, "⚠️ 未配置IMAP密码，跳过接收检测")
            return {'received_count': 0, 'missing_count': expected_count, 'loss_rate': 100}
        
        try:
            # 连接IMAP服务器
            mail = imaplib.IMAP4_SSL('imap.qq.com', 993)
            mail.login(self.config['to_addr'], self.config['imap_password'])
            mail.select('INBOX')
            
            self.progress_updated.emit(70, "✅ IMAP登录成功，开始搜索邮件...")
            
            # 等待并检查邮件
            received_count = 0
            max_checks = 10  # 最多检查10次
            
            for check_num in range(max_checks):
                try:
                    # 搜索发件人的所有邮件
                    search_criteria = f'FROM "{self.config["from_addr"]}"'
                    status, messages = mail.search(None, search_criteria)
                    
                    if status == 'OK':
                        message_ids = messages[0].split()
                        
                        # 过滤测试邮件
                        test_emails = []
                        for msg_id in message_ids[-20:]:  # 只检查最近20封邮件
                            try:
                                status, msg_data = mail.fetch(msg_id, '(RFC822)')
                                if status == 'OK':
                                    email_body = msg_data[0][1]
                                    email_message = email.message_from_bytes(email_body)
                                    
                                    subject = email_message.get('Subject', '')
                                    
                                    # 检查是否是本次测试的邮件
                                    if self.test_id in subject and 'BatchTest' in subject:
                                        test_emails.append(subject)
                            except Exception:
                                continue
                        
                        received_count = len(test_emails)
                        progress = 70 + int(check_num / max_checks * 25)
                        self.progress_updated.emit(progress, f"🔍 第{check_num+1}次检查: 收到 {received_count}/{expected_count} 封测试邮件")
                        
                        if received_count >= expected_count:
                            self.progress_updated.emit(90, f"✅ 已收到所有 {expected_count} 封邮件！")
                            break
                        
                        if check_num < max_checks - 1:
                            wait_time = 30
                            self.progress_updated.emit(progress, f"⏱️ 等待 {wait_time} 秒后再次检查...")
                            time.sleep(wait_time)
                    else:
                        self.progress_updated.emit(70, f"❌ 搜索邮件失败: {status}")
                        break
                        
                except Exception as e:
                    self.progress_updated.emit(70, f"❌ 搜索过程出错: {e}")
                    break
                
            # 正确关闭IMAP连接
            try:
                mail.close()
            except Exception:
                pass
            
            try:
                mail.logout()
            except Exception:
                pass
            
            missing_count = expected_count - received_count
            loss_rate = (missing_count / expected_count * 100) if expected_count > 0 else 0
            
            return {
                'received_count': received_count,
                'missing_count': missing_count,
                'loss_rate': loss_rate
            }
            
        except Exception as e:
            self.progress_updated.emit(80, f"❌ 接收检测失败: {e}")
            return {'received_count': 0, 'missing_count': expected_count, 'loss_rate': 100}

class EmailTestGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("📬 邮件连发测试工具 (修复版)")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建标签页
        tab_widget = QTabWidget()
        central_widget_layout = QVBoxLayout(central_widget)
        central_widget_layout.addWidget(tab_widget)
        
        # 配置标签页
        config_tab = self.create_config_tab()
        tab_widget.addTab(config_tab, "📧 邮件配置")
        
        # 测试标签页
        test_tab = self.create_test_tab()
        tab_widget.addTab(test_tab, "🚀 连发测试")
        
        # 结果标签页
        result_tab = self.create_result_tab()
        tab_widget.addTab(result_tab, "📊 测试结果")
        
        # 初始化配置
        self.load_default_config()
        
    def create_config_tab(self):
        """创建配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 发送配置
        send_group = QGroupBox("📤 发送邮箱配置")
        send_layout = QFormLayout(send_group)
        
        self.smtp_host = QLineEdit("xcs.mail.chinaunicom.cn")
        self.smtp_port = QSpinBox()
        self.smtp_port.setRange(1, 65535)
        self.smtp_port.setValue(465)
        self.use_ssl = QCheckBox("使用SSL")
        self.use_ssl.setChecked(True)
        self.username = QLineEdit("<EMAIL>")
        self.password = QLineEdit("OAbbd520.")
        self.password.setEchoMode(QLineEdit.Password)
        self.from_addr = QLineEdit("<EMAIL>")
        self.to_addr = QLineEdit("<EMAIL>")
        
        send_layout.addRow("SMTP服务器:", self.smtp_host)
        send_layout.addRow("端口:", self.smtp_port)
        send_layout.addRow("", self.use_ssl)
        send_layout.addRow("用户名:", self.username)
        send_layout.addRow("密码:", self.password)
        send_layout.addRow("发件人:", self.from_addr)
        send_layout.addRow("收件人:", self.to_addr)
        
        # 接收配置
        recv_group = QGroupBox("📥 接收检测配置 (可选)")
        recv_layout = QFormLayout(recv_group)
        
        self.enable_recv_check = QCheckBox("启用接收端检测")
        self.imap_password = QLineEdit()
        self.imap_password.setEchoMode(QLineEdit.Password)
        self.imap_password.setPlaceholderText("QQ邮箱授权码（16位）")
        
        recv_layout.addRow("", self.enable_recv_check)
        recv_layout.addRow("QQ邮箱授权码:", self.imap_password)
        
        # 测试按钮
        test_conn_btn = QPushButton("🔍 测试连接")
        test_conn_btn.clicked.connect(self.test_connection)
        
        layout.addWidget(send_group)
        layout.addWidget(recv_group)
        layout.addWidget(test_conn_btn)
        layout.addStretch()
        
        return widget

    def create_test_tab(self):
        """创建测试标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 测试配置
        test_group = QGroupBox("🚀 测试配置")
        test_layout = QFormLayout(test_group)

        self.email_count = QComboBox()
        self.email_count.addItems(["5封", "8封", "10封", "15封", "20封", "30封"])
        self.email_count.setCurrentText("10封")

        test_layout.addRow("连发数量:", self.email_count)

        # 控制按钮
        button_layout = QHBoxLayout()

        self.start_btn = QPushButton("🚀 开始连发测试")
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        self.start_btn.clicked.connect(self.start_test)

        self.stop_btn = QPushButton("⏹️ 停止测试")
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        self.stop_btn.clicked.connect(self.stop_test)

        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(self.stop_btn)
        button_layout.addStretch()

        # 进度显示
        progress_group = QGroupBox("📊 测试进度")
        progress_layout = QVBoxLayout(progress_group)

        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)

        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setFont(QFont("Consolas", 9))

        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.log_text)

        layout.addWidget(test_group)
        layout.addLayout(button_layout)
        layout.addWidget(progress_group)
        layout.addStretch()

        return widget

    def create_result_tab(self):
        """创建结果标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 结果显示
        self.result_text = QTextEdit()
        self.result_text.setFont(QFont("Consolas", 10))
        self.result_text.setReadOnly(True)

        # 操作按钮
        button_layout = QHBoxLayout()

        clear_btn = QPushButton("🗑️ 清空结果")
        clear_btn.clicked.connect(self.clear_results)

        save_btn = QPushButton("💾 保存结果")
        save_btn.clicked.connect(self.save_results)

        button_layout.addWidget(clear_btn)
        button_layout.addWidget(save_btn)
        button_layout.addStretch()

        layout.addWidget(self.result_text)
        layout.addLayout(button_layout)

        return widget

    def load_default_config(self):
        """加载默认配置"""
        pass

    def get_config(self):
        """获取当前配置"""
        return {
            'smtp_host': self.smtp_host.text(),
            'smtp_port': self.smtp_port.value(),
            'use_ssl': self.use_ssl.isChecked(),
            'username': self.username.text(),
            'password': self.password.text(),
            'from_addr': self.from_addr.text(),
            'to_addr': self.to_addr.text(),
            'imap_password': self.imap_password.text() if self.enable_recv_check.isChecked() else None
        }

    def test_connection(self):
        """测试邮件连接"""
        config = self.get_config()

        try:
            # 测试SMTP连接
            if config['use_ssl']:
                server = smtplib.SMTP_SSL(config['smtp_host'], config['smtp_port'], timeout=30)
            else:
                server = smtplib.SMTP(config['smtp_host'], config['smtp_port'], timeout=30)

            server.login(config['username'], config['password'])
            server.quit()

            msg = "✅ SMTP连接测试成功！"

            # 测试IMAP连接（如果启用）
            if self.enable_recv_check.isChecked() and config['imap_password']:
                imap_result = self.test_imap_connection(config['to_addr'], config['imap_password'])
                msg += f"\n{imap_result}"

            QMessageBox.information(self, "连接测试", msg)

        except Exception as e:
            QMessageBox.critical(self, "连接测试失败", f"❌ SMTP连接失败:\n{e}")

    def test_imap_connection(self, email_addr, password):
        """测试IMAP连接"""
        try:
            mail = imaplib.IMAP4_SSL('imap.qq.com', 993)
            mail.login(email_addr, password)

            # 选择收件箱进行测试
            status, _ = mail.select('INBOX')
            if status == 'OK':
                # 获取邮箱信息
                status, count = mail.search(None, 'ALL')
                if status == 'OK':
                    email_count = len(count[0].split()) if count[0] else 0
                    result = f"✅ IMAP连接测试成功！收件箱有 {email_count} 封邮件"
                else:
                    result = "✅ IMAP连接成功，但无法读取邮件数量"
            else:
                result = "✅ IMAP登录成功，但无法选择收件箱"

            # 正确关闭连接
            try:
                mail.close()
            except Exception:
                pass

            try:
                mail.logout()
            except Exception:
                pass

            return result

        except imaplib.IMAP4.error as e:
            return f"❌ IMAP协议错误: {e}"
        except Exception as e:
            return f"❌ IMAP连接失败: {e}"

    def start_test(self):
        """开始测试"""
        config = self.get_config()
        count = int(self.email_count.currentText().replace("封", ""))
        check_received = self.enable_recv_check.isChecked()

        # 验证配置
        if not all([config['smtp_host'], config['username'], config['password'],
                   config['from_addr'], config['to_addr']]):
            QMessageBox.warning(self, "配置错误", "请填写完整的邮件配置信息")
            return

        if check_received and not config['imap_password']:
            QMessageBox.warning(self, "配置错误", "启用接收检测需要填写QQ邮箱授权码")
            return

        # 启动测试线程
        self.worker = EmailTestWorker(config, count, check_received)
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.test_completed.connect(self.test_finished)
        self.worker.start()

        # 更新UI状态
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress_bar.setValue(0)
        self.log_text.clear()
        self.add_log(f"🚀 开始连发测试: {count}封邮件")

    def stop_test(self):
        """停止测试"""
        if hasattr(self, 'worker') and self.worker.isRunning():
            self.worker.terminate()
            self.worker.wait()

        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.add_log("⏹️ 测试已停止")

    def update_progress(self, progress, message):
        """更新进度"""
        self.progress_bar.setValue(progress)
        self.add_log(message)

    def test_finished(self, result):
        """测试完成"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setValue(100)

        # 生成测试报告
        report = self.generate_report(result)
        self.result_text.setText(report)
        self.add_log("✅ 测试完成！请查看结果标签页")

    def generate_report(self, result):
        """生成测试报告"""
        report = f"""
{'='*60}
📊 邮件连发测试报告
{'='*60}

🎯 测试概况:
• 测试ID: {result['test_id']}
• 测试时间: {result['start_time'].strftime('%Y-%m-%d %H:%M:%S')}
• 邮箱类型: 联通企业邮箱

📈 发送结果:
• 计划发送: {result['count']} 封
• 成功发送: {result['success_count']} 封
• 发送失败: {result['failed_count']} 封
• 发送成功率: {result['send_success_rate']:.1f}%

⏱️ 时间统计:
• 发送总耗时: {result['total_time']:.1f} 秒
• 平均每封: {result['avg_send_time']:.1f} 秒
• 发送速度: {result['count']/result['total_time']*60:.1f} 封/分钟
"""

        # 如果有接收检测结果
        if 'received_count' in result:
            report += f"""
📥 接收结果:
• 实际收到: {result['received_count']} 封
• 丢失邮件: {result['missing_count']} 封
• 丢邮件率: {result['loss_rate']:.1f}%
• 端到端成功率: {(result['received_count']/result['count']*100):.1f}%

🏆 综合评估:"""

            overall_success_rate = result['received_count'] / result['count'] * 100
            if overall_success_rate >= 95:
                report += "\n🎉 优秀！邮箱稳定性很好"
            elif overall_success_rate >= 85:
                report += "\n✅ 良好！邮箱稳定性可接受"
            elif overall_success_rate >= 70:
                report += "\n⚠️ 一般！建议优化邮箱配置"
            else:
                report += "\n❌ 较差！强烈建议更换邮箱或优化配置"
        else:
            report += f"""
💡 建议:
请手动检查收件箱 {self.to_addr.text()}
搜索主题包含 '{result['test_id']}' 的邮件
统计实际收到的邮件数量"""

        report += f"\n{'='*60}"
        return report

    def add_log(self, message):
        """添加日志 - 修复版本"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        self.log_text.append(f"[{timestamp}] {message}")

        # 自动滚动到底部 - 修复QTextCursor.End问题
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)

    def clear_results(self):
        """清空结果"""
        self.result_text.clear()
        self.log_text.clear()

    def save_results(self):
        """保存结果"""
        from PySide6.QtWidgets import QFileDialog

        filename, _ = QFileDialog.getSaveFileName(
            self, "保存测试结果",
            f"email_test_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            "文本文件 (*.txt)"
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.result_text.toPlainText())
                QMessageBox.information(self, "保存成功", f"结果已保存到:\n{filename}")
            except Exception as e:
                QMessageBox.critical(self, "保存失败", f"保存失败:\n{e}")

def main():
    app = QApplication(sys.argv)

    # 设置应用样式
    app.setStyleSheet("""
        QMainWindow {
            background-color: #f5f5f5;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        QLineEdit, QSpinBox, QComboBox {
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        QTextEdit {
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        QProgressBar {
            border: 1px solid #ddd;
            border-radius: 3px;
            text-align: center;
        }
        QProgressBar::chunk {
            background-color: #27ae60;
            border-radius: 2px;
        }
    """)

    window = EmailTestGUI()
    window.show()

    sys.exit(app.exec())

if __name__ == "__main__":
    main()
